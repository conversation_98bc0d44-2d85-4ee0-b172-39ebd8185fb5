<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}Talaria_Dashboard{% endblock %}</title>
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='img/favicon-96x96.png') }}" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='img/favicon.svg') }}" />
    <link rel="shortcut icon" href="{{ url_for('static', filename='img/favicon.ico') }}" />
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='img/apple-touch-icon.png') }}" />
    <meta name="apple-mobile-web-app-title" content="Talaria_Dashboard" />

    <!-- Custom style for making the favicon appear round -->
    <style>
        /* For custom round favicons within the page */
        .round-favicon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            overflow: hidden;
            display: inline-block;
        }

        .round-favicon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* SVG mask to create a round favicon */
        svg.favicon-mask {
            position: absolute;
            width: 0;
            height: 0;
        }

        /* For browsers that support mask-image */
        @supports (-webkit-mask-image: url(#mask)) or (mask-image: url(#mask)) {
            link[rel="icon"] {
                -webkit-mask-image: url(#round-mask);
                mask-image: url(#round-mask);
                border-radius: 50%;
            }
        }
    </style>

    <!-- SVG Mask for round favicon -->
    <svg class="favicon-mask" width="0" height="0" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <mask id="round-mask">
                <circle cx="50%" cy="50%" r="50%" fill="white" />
            </mask>
        </defs>
    </svg>
    <!--Custom Css Links-->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/base.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/home.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive-utilities.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/ux-enhancements.css') }}">

    <!-- Custom CSS for pulsing animation -->
    <style>
        @keyframes pulse-red {
            0% {
                background-color: #fee2e2;
                color: #b91c1c;
                box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
            }

            70% {
                background-color: #fecaca;
                color: #dc2626;
                box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
            }

            100% {
                background-color: #fee2e2;
                color: #b91c1c;
                box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
            }
        }

        .support-link {
            background-color: #fee2e2 !important;
            color: #b91c1c !important;
            animation: pulse-red 2s infinite;
            font-weight: bold;
        }

        .support-link:hover {
            background-color: #fecaca !important;
            color: #dc2626 !important;
            animation: none;
        }

        .support-link i {
            color: #dc2626 !important;
        }
    </style>
    <!-- Core Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>

    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    <!-- Flatpickr -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <!-- In base.html -->
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
    <script src="{{ url_for('static', filename='js/background-fallback.js') }}"></script>
    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>
    <script src="{{ url_for('static', filename='js/breadcrumb.js') }}"></script>
    <script src="{{ url_for('static', filename='js/progress-indicator.js') }}"></script>
    <script src="{{ url_for('static', filename='js/keyboard-shortcuts.js') }}"></script>
    <script src="{{ url_for('static', filename='js/offline-mode.js') }}"></script>

    {% block extra_css %}{% endblock %}
</head>

<body class="bg-gray-50" data-user-role="{{ session.get('user_role', '') }}">

    <!-- CSRF Token -->
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

    {% if user_is_authenticated %}
    <!-- Top Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm fixed w-full z-30">
        <div class="max-w-full mx-auto px-4">
            <div class="flex justify-between h-16">
                <!-- Left side -->
                <div class="flex items-center">
                    <button type="button" id="sidebar-toggle"
                        class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700" title="Toggle Sidebar">
                        <i class="fas fa-bars text-gray-600 dark:text-gray-300"></i>
                    </button>
                    <img src="{{ url_for('static', filename='img/logo.gif') }}" alt="Logo" class="h-8 ml-4">
                    <h1 class="text-lg font-semibold ml-3 hidden md:block"></h1>
                </div>

                <!-- Right side - improved with action buttons -->
                <div class="flex items-center space-x-4">
                    <!-- Quick Action Buttons - replacing search bar -->
                    <div class="hidden md:flex space-x-2">
                        {% if check_permission('modify') %}
                        <a href="{{ url_for('label_packing_slip') }}" class="btn-quick-action">
                            <i class="fas fa-tag mr-1"></i> Generate Labels
                        </a>
                        {% endif %}
                        {% if session.get('user_role') == 'admin' %}
                        <a href="{{ url_for('label_generator.index') }}" class="btn-quick-action">
                            <i class="fas fa-tags mr-1"></i> Manual Label Generator
                        </a>
                        {% endif %}
                        <a href="{{ url_for('inventory_management') }}" class="btn-quick-action">
                            <i class="fas fa-boxes mr-1"></i> Inventory
                        </a>
                        <a href="{{ url_for('shipment.shipment_dashboard') }}" class="btn-quick-action">
                            <i class="fas fa-truck mr-1"></i> Shipments Task
                        </a>
                        <a href="{{ url_for('support') }}" class="btn-quick-action support-link">
                            <i class="fas fa-headset mr-1"></i> Support
                        </a>
                        <a href="{{ url_for('chatbot') }}" class="btn-quick-action">
                            <i class="fas fa-robot mr-1"></i> Chat
                        </a>
                        {% if check_permission('modify') %}
                        <a href="{{ url_for('rfq.rfq_automation') }}" class="btn-quick-action">
                            <i class="fas fa-envelope-open-text mr-1"></i> RFQ
                        </a>
                        {% endif %}
                    </div>

                    <!-- Mobile Quick Actions - Dropdown instead of buttons for small screens -->
                    <div class="d-block d-md-none relative">
                        <button type="button" id="mobile-menu-toggle"
                            class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 touch-target" title="Menu">
                            <i class="fas fa-ellipsis-v text-gray-600 dark:text-gray-300"></i>
                        </button>
                        <div id="mobile-menu-dropdown"
                            class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            {% if check_permission('modify') %}
                            <a href="{{ url_for('label_packing_slip') }}"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-tag mr-2"></i>Generate Labels
                            </a>
                            {% endif %}
                            {% if session.get('user_role') == 'admin' %}
                            <a href="{{ url_for('label_generator.index') }}"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-tags mr-2"></i>Manual Label Generator
                            </a>
                            {% endif %}
                            <a href="{{ url_for('inventory_management') }}"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-boxes mr-2"></i>Inventory
                            </a>
                            <a href="{{ url_for('support') }}"
                                class="block px-4 py-2 text-sm support-link">
                                <i class="fas fa-headset mr-2"></i>Support
                            </a>
                            <a href="{{ url_for('chatbot') }}"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-robot mr-2"></i>Chat
                            </a>
                            {% if check_permission('modify') %}
                            <a href="{{ url_for('rfq.rfq_automation') }}"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-envelope-open-text mr-2"></i>RFQ Automation
                            </a>
                            {% endif %}
                        </div>
                        <!--Shipment Management-->
                        <div class="hidden md:flex space-x-2">
                            <a href="{{ url_for('shipment.shipment_dashboard') }}" class="btn-quick-action">
                                <i class="fas fa-truck mr-1"></i> Shipments Task
                            </a>
                        </div>

                    </div>

                    <!-- Connection Status Indicator -->
                    <div id="connection-status-container" class="hidden md:block">
                        <!-- Status indicator will be inserted here by JavaScript -->
                    </div>

                    <!-- Notifications -->
                    <button type="button"
                        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 relative touch-target"
                        title="Notifications">
                        <i class="fas fa-bell text-gray-600 dark:text-gray-300"></i>
                        <span class="notification-badge"></span>
                    </button>

                    <!-- User Menu -->
                    <div class="relative">
                        <button type="button" id="user-menu-button"
                            class="flex items-center space-x-2 focus:outline-none">
                            <span class="hidden md:block text-sm">{{ get_user_info().name }}</span>
                            <div
                                class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center overflow-hidden">
                                {% if get_user_info().profile_image %}
                                <img src="{{ url_for('profile_image_file', filename=get_user_info().profile_image) }}"
                                    alt="Profile" class="w-full h-full object-cover">
                                {% else %}
                                <span class="text-blue-600 text-sm font-bold">{{ get_user_info().name[:1] }}</span>
                                {% endif %}
                            </div>
                        </button>
                        <!-- User Dropdown Menu -->
                        <div id="user-dropdown"
                            class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="{{ url_for('preferences') }}"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-2"></i>Profile Settings
                            </a>
                            <a href="{{ url_for('auth.logout') }}"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>

                    <!-- Theme Toggle -->
                    <button type="button" id="loginThemeToggle"
                        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" title="Toggle Theme">
                        <i class="fas fa-sun text-yellow-500 dark:hidden" title="Light Mode" aria-hidden="true"></i>
                        <i class="fas fa-moon text-blue-500 hidden dark:block"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <aside class="sidebar fixed left-0 top-16 shadow-lg">
        <!-- Collapse Button -->
        <div class="collapse-btn" id="collapse-btn">
            <i class="fas fa-chevron-left text-sm"></i>
        </div>

        <!-- Talaria Logo Section - centered and prominent -->
        <div class="p-5 border-b border-gray-200 dark:border-gray-700 flex justify-center">
            <div class="logo-iridescent w-16 h-16">
                <img src="{{ url_for('static', filename='img/logoTalaria.jpeg') }}" alt="Talaria Logo"
                    class="w-full h-full rounded-full object-contain">
            </div>
        </div>

        <!-- Navigation Container -->
        <div class="sidebar-nav-container">
            <!-- Main Menu -->
            <p class="section-header sidebar-text">Main Menu</p>
            <nav class="space-y-1">
                <a href="{{ url_for('home') }}" class="nav-link {% if request.endpoint == 'home' %}active{% endif %}">
                    <i class="fas fa-home"></i>
                    <span class="sidebar-text">Dashboard</span>
                </a>

                <a href="{{ url_for('about') }}" class="nav-link">
                    <i class="fas fa-info-circle"></i>
                    <span class="sidebar-text">About</span>
                </a>
                <a href="{{ url_for('chatbot') }}"
                    class="nav-link {% if request.endpoint == 'chatbot' %}active{% endif %}">
                    <i class="fas fa-robot"></i>
                    <span class="sidebar-text">ChatBot</span>
                </a>
                <a href="{{ url_for('shipment_statistics') }}"
                    class="nav-link {% if request.endpoint == 'shipment_statistics' %}active{% endif %}">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">Statistics</span>
                </a>
            </nav>

            <!-- Tools & Features -->
            <p class="section-header sidebar-text">Tools & Features</p>
            <nav class="space-y-1">
                <a href="{{ url_for('inventory_management') }}" class="nav-link">
                    <i class="fas fa-boxes"></i>
                    <span class="sidebar-text">Inventory</span>
                </a>
                {% if session.get('user_role') == 'admin' %}
                <a href="{{ url_for('label_generator.index') }}" class="nav-link">
                    <i class="fas fa-tags"></i>
                    <span class="sidebar-text">Manual Label Generator</span>
                </a>
                {% endif %}
                <a href="{{ url_for('shipment.shipment_dashboard') }}"
                    class="nav-link {% if request.endpoint == 'shipment.shipment_dashboard' %}active{% endif %}">
                    <i class="fas fa-truck"></i>
                    <span class="sidebar-text">Shipment Task</span>
                </a>
                <a href="{{ url_for('label_packing_slip') }}"
                    class="nav-link {% if request.endpoint == 'label_packing_slip' %}active{% endif %}">
                    <i class="fas fa-box-open"></i>
                    <span class="sidebar-text">Print Label with Asana</span>
                </a>
                <a href="{{ url_for('location_management') }}" class="nav-link">
                    <i class="fas fa-map-marker-alt"></i>
                    <span class="sidebar-text">Location Management</span>
                </a>
                <a href="{{ url_for('ups_views.shipping_page') }}"
                    class="nav-link {% if request.endpoint == 'ups_views.shipping_page' %}active{% endif %}">
                    <i class="fas fa-shipping-fast"></i>
                    <span class="sidebar-text">UPS Shipping</span>
                </a>
                <a href="{{ url_for('label_templates') }}" class="nav-link">
                    <i class="fas fa-tags"></i>
                    <span class="sidebar-text">Label Templates</span>
                </a>
                {% if check_permission('modify') %}
                <a href="{{ url_for('rfq.rfq_automation') }}" class="nav-link">
                    <i class="fas fa-envelope-open-text"></i>
                    <span class="sidebar-text">RFQ Email Automation</span>
                </a>
                {% endif %}
            </nav>

            <!-- Support & Help -->
            <p class="section-header sidebar-text">Support & Help</p>
            <nav class="space-y-1">
                <a href="{{ url_for('documentation') }}" class="nav-link">
                    <i class="fas fa-book"></i>
                    <span class="sidebar-text">Documentation</span>
                </a>
                <a href="{{ url_for('faqs') }}" class="nav-link">
                    <i class="fas fa-question-circle"></i>
                    <span class="sidebar-text">FAQs</span>
                </a>
                <a href="{{ url_for('support') }}" class="nav-link support-link">
                    <i class="fas fa-headset"></i>
                    <span class="sidebar-text">Support</span>
                </a>
            </nav>

            <!-- Settings -->
            <p class="section-header sidebar-text">Settings</p>
            <nav class="space-y-1">
                <a href="{{ url_for('settings') }}" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">General Settings</span>
                </a>
                <a href="{{ url_for('preferences') }}" class="nav-link">
                    <i class="fas fa-sliders-h"></i>
                    <span class="sidebar-text">Preferences</span>
                </a>

                <!-- Add the User Management link here -->
                {% if check_permission('modify') %}
                <a href="{{ url_for('users.manage_users') }}" class="nav-link">
                    <i class="fas fa-users mr-2 text-gray-400"></i>
                    <span class="sidebar-text">User Management</span>
                </a>
                {% endif %}
            </nav>
        </div>

        <!-- System & Testing -->
        <p class="section-header sidebar-text">System & Testing</p>
        <nav class="space-y-1">
        </nav>
        </nav>

        <!-- Bottom Section -->
        <div class="sidebar-bottom">
            <div class="flex items-center justify-between">
                <a href="{{ url_for('auth.logout') }}"
                    class="flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    <span class="sidebar-text">Logout</span>
                </a>
                <span class="text-xs text-gray-400 sidebar-text">v2.0.0</span>
            </div>
        </div>
    </aside>
    {% else %}
    <!-- Simplified header for login page -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm fixed w-full z-30">
        <div class="max-w-full mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <img src="{{ url_for('static', filename='img/logo.gif') }}" alt="Logo" class="h-8 ml-4">
                    <h1 class="text-lg font-semibold ml-3">Talaria Dashboard</h1>
                </div>
                <!-- Theme Toggle -->
                <div class="flex items-center">
                    <button type="button" id="themeToggle"
                        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" title="Toggle Theme">
                        <i class="fas fa-sun text-yellow-500 dark:hidden"></i>
                        <i class="fas fa-moon text-blue-500 hidden dark:block"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <main id="main-content"
        class="{% if user_is_authenticated %}md:ml-64{% endif %} pt-16 min-h-screen transition-all duration-300">
        <div class="p-2 sm:p-4 md:p-6">
            <!-- Breadcrumb Navigation -->
            {% if user_is_authenticated %}
            <nav class="breadcrumb-nav mb-4" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2 text-sm text-gray-500">
                    <li>
                        <a href="{{ url_for('home') }}" class="hover:text-blue-600 transition-colors">
                            <i class="fas fa-home mr-1"></i>Dashboard
                        </a>
                    </li>
                    {% block breadcrumb %}{% endblock %}
                </ol>
            </nav>
            {% endif %}

            <!-- Progress Indicator Container -->
            <div id="progress-container" class="hidden mb-4">
                <div class="bg-white rounded-lg shadow-sm border p-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 id="progress-title" class="text-sm font-medium text-gray-900">Processing...</h3>
                        <span id="progress-percentage" class="text-sm text-gray-500">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="progress-steps" class="mt-3 flex justify-between text-xs text-gray-500">
                        <!-- Steps will be dynamically added here -->
                    </div>
                </div>
            </div>

            <!-- Warning for small screens if needed -->
            <div class="d-block d-md-none bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 hidden"
                id="mobile-warning">
                <p class="font-bold">Mobile View</p>
                <p>Some features work best on larger screens. Rotate your device for a better experience.</p>
                <button onclick="document.getElementById('mobile-warning').style.display='none'"
                    class="mt-2 text-yellow-700">
                    <i class="fas fa-times"></i> Dismiss
                </button>
            </div>

            <!-- Keyboard Shortcuts Help Modal -->
            <div id="shortcuts-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold">Keyboard Shortcuts</h3>
                                <button id="close-shortcuts" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h4 class="font-medium mb-2">Navigation</h4>
                                    <div class="space-y-1 text-sm">
                                        <div class="flex justify-between">
                                            <span>Dashboard</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded">Alt + H</kbd>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Inventory</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded">Alt + I</kbd>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Generate Labels</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded">Alt + L</kbd>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Shipments</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded">Alt + S</kbd>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="font-medium mb-2">Actions</h4>
                                    <div class="space-y-1 text-sm">
                                        <div class="flex justify-between">
                                            <span>Search</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded">Ctrl + K</kbd>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Save</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded">Ctrl + S</kbd>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Help</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded">?</kbd>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Toggle Sidebar</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded">Ctrl + B</kbd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Sidebar Backdrop -->
    <div id="sidebar-backdrop" class="fixed inset-0 bg-black bg-opacity-50 z-10 hidden"></div>
    <script src="{{ url_for('static', filename='js/permissions.js') }}"></script>
    <script src="{{ url_for('static', filename='js/base.js') }}"></script>
    <script src="{{ url_for('static', filename='js/theme.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>

</html>