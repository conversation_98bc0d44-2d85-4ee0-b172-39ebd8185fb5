/**
 * Workflow Automation System
 * Smart rule-based automation with event-driven triggers
 */

class WorkflowAutomationEngine {
  constructor() {
    this.workflows = new Map();
    this.eventListeners = new Map();
    this.actionHandlers = new Map();
    this.conditionEvaluators = new Map();
    this.executionHistory = [];
    this.isEnabled = true;
    
    this.init();
  }

  init() {
    this.setupDefaultActionHandlers();
    this.setupDefaultConditionEvaluators();
    this.setupEventListeners();
    this.loadWorkflows();
    console.log('🤖 Workflow Automation Engine initialized');
  }

  /**
   * Register a new workflow
   */
  registerWorkflow(workflow) {
    if (!workflow.id || !workflow.name || !workflow.triggers || !workflow.actions) {
      throw new Error('Invalid workflow configuration');
    }

    // Validate triggers and actions
    this.validateWorkflow(workflow);

    this.workflows.set(workflow.id, {
      ...workflow,
      enabled: workflow.enabled !== false,
      createdAt: new Date().toISOString(),
      lastExecuted: null,
      executionCount: 0
    });

    console.log(`✅ Workflow registered: ${workflow.name}`);
    this.saveWorkflows();
  }

  /**
   * Event-driven trigger system
   */
  setupEventListeners() {
    // Inventory change events
    document.addEventListener('inventoryUpdated', (event) => {
      this.handleEvent('inventory.updated', event.detail);
    });

    document.addEventListener('inventoryAdded', (event) => {
      this.handleEvent('inventory.added', event.detail);
    });

    document.addEventListener('inventoryDeleted', (event) => {
      this.handleEvent('inventory.deleted', event.detail);
    });

    // Location change events
    document.addEventListener('locationChanged', (event) => {
      this.handleEvent('location.changed', event.detail);
    });

    // Shipment events
    document.addEventListener('shipmentCreated', (event) => {
      this.handleEvent('shipment.created', event.detail);
    });

    document.addEventListener('shipmentStatusChanged', (event) => {
      this.handleEvent('shipment.status_changed', event.detail);
    });

    // Time-based events (check every minute)
    setInterval(() => {
      this.handleEvent('time.minute', { timestamp: new Date().toISOString() });
    }, 60000);

    // Daily events (check every hour for daily triggers)
    setInterval(() => {
      const now = new Date();
      if (now.getHours() === 0 && now.getMinutes() < 5) {
        this.handleEvent('time.daily', { timestamp: now.toISOString() });
      }
    }, 300000); // Check every 5 minutes
  }

  /**
   * Handle incoming events and trigger workflows
   */
  async handleEvent(eventType, eventData) {
    if (!this.isEnabled) return;

    console.log(`🔔 Event received: ${eventType}`, eventData);

    // Find workflows that should be triggered by this event
    const triggeredWorkflows = Array.from(this.workflows.values()).filter(workflow => {
      if (!workflow.enabled) return false;

      return workflow.triggers.some(trigger => {
        if (trigger.type !== eventType) return false;
        
        // Evaluate trigger conditions
        if (trigger.conditions) {
          return this.evaluateConditions(trigger.conditions, eventData);
        }
        
        return true;
      });
    });

    // Execute triggered workflows
    for (const workflow of triggeredWorkflows) {
      try {
        await this.executeWorkflow(workflow, eventType, eventData);
      } catch (error) {
        console.error(`❌ Workflow execution failed: ${workflow.name}`, error);
        this.logExecution(workflow.id, eventType, eventData, false, error.message);
      }
    }
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(workflow, eventType, eventData) {
    console.log(`🚀 Executing workflow: ${workflow.name}`);

    const executionContext = {
      workflowId: workflow.id,
      eventType,
      eventData,
      timestamp: new Date().toISOString(),
      variables: { ...workflow.variables }
    };

    // Execute pre-conditions if any
    if (workflow.preConditions) {
      const conditionsMet = this.evaluateConditions(workflow.preConditions, eventData);
      if (!conditionsMet) {
        console.log(`⏭️ Pre-conditions not met for workflow: ${workflow.name}`);
        return;
      }
    }

    // Execute actions sequentially
    for (const action of workflow.actions) {
      try {
        await this.executeAction(action, executionContext);
      } catch (error) {
        console.error(`❌ Action failed in workflow ${workflow.name}:`, error);
        
        if (action.onError === 'stop') {
          throw error;
        } else if (action.onError === 'continue') {
          continue;
        }
      }
    }

    // Update workflow statistics
    workflow.lastExecuted = new Date().toISOString();
    workflow.executionCount++;

    this.logExecution(workflow.id, eventType, eventData, true);
    console.log(`✅ Workflow completed: ${workflow.name}`);
  }

  /**
   * Execute a single action
   */
  async executeAction(action, context) {
    const handler = this.actionHandlers.get(action.type);
    if (!handler) {
      throw new Error(`Unknown action type: ${action.type}`);
    }

    console.log(`🎯 Executing action: ${action.type}`);
    
    // Prepare action parameters with variable substitution
    const parameters = this.substituteVariables(action.parameters, context);
    
    return await handler(parameters, context);
  }

  /**
   * Setup default action handlers
   */
  setupDefaultActionHandlers() {
    // Email notification handler
    this.actionHandlers.set('email.send', async (params, context) => {
      const emailData = {
        to: params.to,
        cc: params.cc,
        subject: params.subject,
        body: params.body,
        template: params.template,
        attachments: params.attachments
      };

      const response = await fetch('/api/workflow/actions/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify(emailData)
      });

      if (!response.ok) {
        throw new Error(`Email sending failed: ${response.statusText}`);
      }

      return await response.json();
    });

    // Notification handler
    this.actionHandlers.set('notification.show', async (params, context) => {
      if (typeof Swal !== 'undefined') {
        Swal.fire({
          title: params.title,
          text: params.message,
          icon: params.type || 'info',
          toast: params.toast !== false,
          position: params.position || 'top-end',
          showConfirmButton: params.showConfirmButton !== false,
          timer: params.timer || 5000
        });
      }
    });

    // API call handler
    this.actionHandlers.set('api.call', async (params, context) => {
      const response = await fetch(params.url, {
        method: params.method || 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken(),
          ...params.headers
        },
        body: params.body ? JSON.stringify(params.body) : undefined
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.statusText}`);
      }

      return await response.json();
    });

    // Database update handler
    this.actionHandlers.set('database.update', async (params, context) => {
      const response = await fetch('/api/workflow/actions/database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify({
          table: params.table,
          where: params.where,
          data: params.data,
          operation: params.operation || 'update'
        })
      });

      if (!response.ok) {
        throw new Error(`Database update failed: ${response.statusText}`);
      }

      return await response.json();
    });

    // Delay handler
    this.actionHandlers.set('system.delay', async (params, context) => {
      const delay = params.seconds * 1000;
      return new Promise(resolve => setTimeout(resolve, delay));
    });

    // Log handler
    this.actionHandlers.set('system.log', async (params, context) => {
      console.log(`📝 Workflow Log [${context.workflowId}]:`, params.message);

      // Send to server logging if configured
      if (params.serverLog) {
        await fetch('/api/workflow/log', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': this.getCSRFToken()
          },
          body: JSON.stringify({
            workflowId: context.workflowId,
            level: params.level || 'info',
            message: params.message,
            context: context
          })
        });
      }
    });

    // ODOO Integration handlers
    this.actionHandlers.set('odoo.create_record', async (params, context) => {
      const response = await fetch('/api/workflow/actions/odoo/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify({
          model: params.model,
          data: params.data,
          context: params.context || {}
        })
      });

      if (!response.ok) {
        throw new Error(`ODOO record creation failed: ${response.statusText}`);
      }

      return await response.json();
    });

    this.actionHandlers.set('odoo.update_record', async (params, context) => {
      const response = await fetch('/api/workflow/actions/odoo/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify({
          model: params.model,
          record_id: params.record_id,
          data: params.data,
          context: params.context || {}
        })
      });

      if (!response.ok) {
        throw new Error(`ODOO record update failed: ${response.statusText}`);
      }

      return await response.json();
    });

    this.actionHandlers.set('odoo.search_records', async (params, context) => {
      const response = await fetch('/api/workflow/actions/odoo/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify({
          model: params.model,
          domain: params.domain || [],
          fields: params.fields || [],
          limit: params.limit || 100,
          offset: params.offset || 0
        })
      });

      if (!response.ok) {
        throw new Error(`ODOO search failed: ${response.statusText}`);
      }

      return await response.json();
    });

    this.actionHandlers.set('odoo.call_method', async (params, context) => {
      const response = await fetch('/api/workflow/actions/odoo/call', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify({
          model: params.model,
          method: params.method,
          args: params.args || [],
          kwargs: params.kwargs || {}
        })
      });

      if (!response.ok) {
        throw new Error(`ODOO method call failed: ${response.statusText}`);
      }

      return await response.json();
    });

    // Asana Integration handlers (enhanced)
    this.actionHandlers.set('asana.create_task', async (params, context) => {
      const response = await fetch('/api/workflow/actions/asana/create_task', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify({
          name: params.name,
          notes: params.notes,
          project_gid: params.project_gid,
          assignee: params.assignee,
          due_date: params.due_date,
          tags: params.tags || []
        })
      });

      if (!response.ok) {
        throw new Error(`Asana task creation failed: ${response.statusText}`);
      }

      return await response.json();
    });

    this.actionHandlers.set('asana.update_task', async (params, context) => {
      const response = await fetch('/api/workflow/actions/asana/update_task', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify({
          task_gid: params.task_gid,
          data: params.data
        })
      });

      if (!response.ok) {
        throw new Error(`Asana task update failed: ${response.statusText}`);
      }

      return await response.json();
    });
  }

  /**
   * Setup default condition evaluators
   */
  setupDefaultConditionEvaluators() {
    // Comparison operators
    this.conditionEvaluators.set('equals', (value, expected) => value === expected);
    this.conditionEvaluators.set('not_equals', (value, expected) => value !== expected);
    this.conditionEvaluators.set('greater_than', (value, expected) => Number(value) > Number(expected));
    this.conditionEvaluators.set('less_than', (value, expected) => Number(value) < Number(expected));
    this.conditionEvaluators.set('greater_equal', (value, expected) => Number(value) >= Number(expected));
    this.conditionEvaluators.set('less_equal', (value, expected) => Number(value) <= Number(expected));

    // String operators
    this.conditionEvaluators.set('contains', (value, expected) => 
      String(value).toLowerCase().includes(String(expected).toLowerCase()));
    this.conditionEvaluators.set('starts_with', (value, expected) => 
      String(value).toLowerCase().startsWith(String(expected).toLowerCase()));
    this.conditionEvaluators.set('ends_with', (value, expected) => 
      String(value).toLowerCase().endsWith(String(expected).toLowerCase()));
    this.conditionEvaluators.set('regex', (value, pattern) => new RegExp(pattern).test(String(value)));

    // Array operators
    this.conditionEvaluators.set('in', (value, array) => Array.isArray(array) && array.includes(value));
    this.conditionEvaluators.set('not_in', (value, array) => Array.isArray(array) && !array.includes(value));

    // Existence operators
    this.conditionEvaluators.set('exists', (value) => value !== null && value !== undefined && value !== '');
    this.conditionEvaluators.set('not_exists', (value) => value === null || value === undefined || value === '');

    // Date operators
    this.conditionEvaluators.set('date_before', (value, expected) => new Date(value) < new Date(expected));
    this.conditionEvaluators.set('date_after', (value, expected) => new Date(value) > new Date(expected));
    this.conditionEvaluators.set('date_between', (value, range) => {
      const date = new Date(value);
      return date >= new Date(range.start) && date <= new Date(range.end);
    });

    // Time-based operators
    this.conditionEvaluators.set('time_of_day', (value, timeRange) => {
      const hour = new Date().getHours();
      return hour >= timeRange.start && hour <= timeRange.end;
    });

    this.conditionEvaluators.set('day_of_week', (value, days) => {
      const dayOfWeek = new Date().getDay(); // 0 = Sunday, 6 = Saturday
      return Array.isArray(days) && days.includes(dayOfWeek);
    });
  }

  /**
   * Evaluate conditions using the condition evaluators
   */
  evaluateConditions(conditions, data) {
    if (!conditions || conditions.length === 0) return true;

    const operator = conditions.operator || 'and';
    const rules = conditions.rules || conditions;

    if (operator === 'and') {
      return rules.every(rule => this.evaluateRule(rule, data));
    } else if (operator === 'or') {
      return rules.some(rule => this.evaluateRule(rule, data));
    }

    return false;
  }

  /**
   * Evaluate a single rule
   */
  evaluateRule(rule, data) {
    const value = this.getValueFromPath(data, rule.field);
    const evaluator = this.conditionEvaluators.get(rule.operator);

    if (!evaluator) {
      console.warn(`Unknown condition operator: ${rule.operator}`);
      return false;
    }

    return evaluator(value, rule.value);
  }

  /**
   * Get value from object using dot notation path
   */
  getValueFromPath(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Substitute variables in parameters
   */
  substituteVariables(parameters, context) {
    if (typeof parameters === 'string') {
      return this.replaceVariables(parameters, context);
    }

    if (Array.isArray(parameters)) {
      return parameters.map(item => this.substituteVariables(item, context));
    }

    if (typeof parameters === 'object' && parameters !== null) {
      const result = {};
      for (const [key, value] of Object.entries(parameters)) {
        result[key] = this.substituteVariables(value, context);
      }
      return result;
    }

    return parameters;
  }

  /**
   * Replace variables in string templates
   */
  replaceVariables(template, context) {
    if (typeof template !== 'string') return template;

    return template.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
      const value = this.getValueFromPath(context, path.trim());
      return value !== undefined ? value : match;
    });
  }

  /**
   * Validate workflow configuration
   */
  validateWorkflow(workflow) {
    // Validate triggers
    if (!Array.isArray(workflow.triggers) || workflow.triggers.length === 0) {
      throw new Error('Workflow must have at least one trigger');
    }

    // Validate actions
    if (!Array.isArray(workflow.actions) || workflow.actions.length === 0) {
      throw new Error('Workflow must have at least one action');
    }

    // Validate action types
    for (const action of workflow.actions) {
      if (!action.type) {
        throw new Error('Action must have a type');
      }
    }
  }

  /**
   * Log workflow execution
   */
  logExecution(workflowId, eventType, eventData, success, error = null) {
    const execution = {
      id: this.generateId(),
      workflowId,
      eventType,
      eventData,
      success,
      error,
      timestamp: new Date().toISOString()
    };

    this.executionHistory.unshift(execution);

    // Keep only last 1000 executions
    if (this.executionHistory.length > 1000) {
      this.executionHistory = this.executionHistory.slice(0, 1000);
    }

    // Save to localStorage
    this.saveExecutionHistory();
  }

  /**
   * Get workflow execution statistics
   */
  getExecutionStats(workflowId = null) {
    const executions = workflowId
      ? this.executionHistory.filter(e => e.workflowId === workflowId)
      : this.executionHistory;

    const total = executions.length;
    const successful = executions.filter(e => e.success).length;
    const failed = total - successful;
    const successRate = total > 0 ? (successful / total * 100).toFixed(2) : 0;

    return {
      total,
      successful,
      failed,
      successRate: `${successRate}%`,
      lastExecution: executions[0]?.timestamp || null
    };
  }

  /**
   * Enable/disable workflow automation
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    console.log(`🤖 Workflow automation ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get all workflows
   */
  getWorkflows() {
    return Array.from(this.workflows.values());
  }

  /**
   * Get workflow by ID
   */
  getWorkflow(id) {
    return this.workflows.get(id);
  }

  /**
   * Update workflow
   */
  updateWorkflow(id, updates) {
    const workflow = this.workflows.get(id);
    if (!workflow) {
      throw new Error(`Workflow not found: ${id}`);
    }

    const updatedWorkflow = { ...workflow, ...updates };
    this.validateWorkflow(updatedWorkflow);

    this.workflows.set(id, updatedWorkflow);
    this.saveWorkflows();

    console.log(`✅ Workflow updated: ${updatedWorkflow.name}`);
  }

  /**
   * Delete workflow
   */
  deleteWorkflow(id) {
    const workflow = this.workflows.get(id);
    if (!workflow) {
      throw new Error(`Workflow not found: ${id}`);
    }

    this.workflows.delete(id);
    this.saveWorkflows();

    console.log(`🗑️ Workflow deleted: ${workflow.name}`);
  }

  /**
   * Trigger workflow manually
   */
  async triggerWorkflow(id, eventData = {}) {
    const workflow = this.workflows.get(id);
    if (!workflow) {
      throw new Error(`Workflow not found: ${id}`);
    }

    if (!workflow.enabled) {
      throw new Error(`Workflow is disabled: ${workflow.name}`);
    }

    await this.executeWorkflow(workflow, 'manual', eventData);
  }

  /**
   * Save workflows to localStorage
   */
  saveWorkflows() {
    try {
      const workflowsArray = Array.from(this.workflows.entries());
      localStorage.setItem('talaria_workflows', JSON.stringify(workflowsArray));
    } catch (error) {
      console.error('Failed to save workflows:', error);
    }
  }

  /**
   * Load workflows from localStorage
   */
  loadWorkflows() {
    try {
      const saved = localStorage.getItem('talaria_workflows');
      if (saved) {
        const workflowsArray = JSON.parse(saved);
        this.workflows = new Map(workflowsArray);
        console.log(`📂 Loaded ${this.workflows.size} workflows`);
      }
    } catch (error) {
      console.error('Failed to load workflows:', error);
    }
  }

  /**
   * Save execution history to localStorage
   */
  saveExecutionHistory() {
    try {
      localStorage.setItem('talaria_workflow_history', JSON.stringify(this.executionHistory));
    } catch (error) {
      console.error('Failed to save execution history:', error);
    }
  }

  /**
   * Load execution history from localStorage
   */
  loadExecutionHistory() {
    try {
      const saved = localStorage.getItem('talaria_workflow_history');
      if (saved) {
        this.executionHistory = JSON.parse(saved);
      }
    } catch (error) {
      console.error('Failed to load execution history:', error);
    }
  }

  /**
   * Generate unique ID
   */
  generateId() {
    return 'wf_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get CSRF token
   */
  getCSRFToken() {
    const tokenMatch = document.cookie.match(/csrftoken=([^;]+)/);
    if (tokenMatch) return tokenMatch[1];

    const csrfInput = document.querySelector('meta[name="csrf-token"]');
    if (csrfInput) return csrfInput.getAttribute("content");

    const flaskCsrf = document.querySelector('input[name="csrf_token"]');
    if (flaskCsrf) return flaskCsrf.value;

    return "";
  }
}

/**
 * Workflow Builder - Visual workflow creation interface
 */
class WorkflowBuilder {
  constructor(engine) {
    this.engine = engine;
    this.currentWorkflow = null;
    this.isEditing = false;
  }

  /**
   * Open workflow builder modal
   */
  openBuilder(workflowId = null) {
    this.isEditing = !!workflowId;
    this.currentWorkflow = workflowId ? this.engine.getWorkflow(workflowId) : this.getDefaultWorkflow();

    this.showBuilderModal();
  }

  /**
   * Get default workflow template
   */
  getDefaultWorkflow() {
    return {
      id: this.engine.generateId(),
      name: '',
      description: '',
      enabled: true,
      triggers: [],
      actions: [],
      preConditions: null,
      variables: {}
    };
  }

  /**
   * Show workflow builder modal
   */
  showBuilderModal() {
    const modalHtml = this.generateBuilderHTML();

    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: this.isEditing ? 'Edit Workflow' : 'Create New Workflow',
        html: modalHtml,
        width: '90%',
        showCancelButton: true,
        confirmButtonText: this.isEditing ? 'Update Workflow' : 'Create Workflow',
        cancelButtonText: 'Cancel',
        didOpen: () => {
          this.initializeBuilder();
        },
        preConfirm: () => {
          return this.saveWorkflow();
        }
      });
    }
  }

  /**
   * Generate builder HTML
   */
  generateBuilderHTML() {
    return `
      <div class="workflow-builder">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Basic Information -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">Basic Information</h3>

            <div>
              <label class="block text-sm font-medium mb-2">Workflow Name</label>
              <input type="text" id="workflow-name" class="w-full px-3 py-2 border rounded-lg"
                     value="${this.currentWorkflow.name}" placeholder="Enter workflow name">
            </div>

            <div>
              <label class="block text-sm font-medium mb-2">Description</label>
              <textarea id="workflow-description" class="w-full px-3 py-2 border rounded-lg" rows="3"
                        placeholder="Describe what this workflow does">${this.currentWorkflow.description}</textarea>
            </div>

            <div class="flex items-center">
              <input type="checkbox" id="workflow-enabled" ${this.currentWorkflow.enabled ? 'checked' : ''}>
              <label for="workflow-enabled" class="ml-2 text-sm">Enable this workflow</label>
            </div>
          </div>

          <!-- Triggers -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">Triggers</h3>
            <div id="triggers-container">
              ${this.generateTriggersHTML()}
            </div>
            <button type="button" onclick="workflowBuilder.addTrigger()"
                    class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
              Add Trigger
            </button>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-6">
          <h3 class="text-lg font-semibold mb-4">Actions</h3>
          <div id="actions-container">
            ${this.generateActionsHTML()}
          </div>
          <button type="button" onclick="workflowBuilder.addAction()"
                  class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
            Add Action
          </button>
        </div>
      </div>
    `;
  }
