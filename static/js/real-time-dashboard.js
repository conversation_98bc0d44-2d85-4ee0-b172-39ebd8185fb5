// Real-Time Dashboard Enhancement for Talaria
// Provides live updates for inventory metrics, notifications, and system status

class RealTimeDashboard {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.updateQueue = [];
    this.lastUpdateTime = null;
    
    this.init();
  }

  init() {
    this.connectSocket();
    this.setupEventListeners();
    this.startPeriodicUpdates();
    this.setupVisibilityChangeHandler();
  }

  connectSocket() {
    try {
      // Initialize SocketIO connection
      this.socket = io({
        transports: ['websocket', 'polling'],
        upgrade: true,
        rememberUpgrade: true
      });

      this.socket.on('connect', () => {
        console.log('✅ Real-time dashboard connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.showConnectionStatus('connected');
        this.requestInitialData();
      });

      this.socket.on('disconnect', () => {
        console.log('❌ Real-time dashboard disconnected');
        this.isConnected = false;
        this.showConnectionStatus('disconnected');
        this.attemptReconnect();
      });

      // Listen for real-time inventory updates
      this.socket.on('inventory_update', (data) => {
        this.handleInventoryUpdate(data);
      });

      // Listen for shipment updates
      this.socket.on('shipment_update', (data) => {
        this.handleShipmentUpdate(data);
      });

      // Listen for system notifications
      this.socket.on('system_notification', (data) => {
        this.handleSystemNotification(data);
      });

      // Listen for dashboard metrics updates
      this.socket.on('dashboard_metrics', (data) => {
        this.updateDashboardMetrics(data);
      });

      // Listen for user activity updates
      this.socket.on('user_activity', (data) => {
        this.handleUserActivity(data);
      });

    } catch (error) {
      console.error('Failed to initialize socket connection:', error);
      this.showConnectionStatus('error');
    }
  }

  requestInitialData() {
    if (this.isConnected) {
      this.socket.emit('request_dashboard_data', {
        timestamp: Date.now(),
        page: window.location.pathname
      });
    }
  }

  handleInventoryUpdate(data) {
    console.log('📦 Inventory update received:', data);
    
    // Update inventory metrics with smooth animations
    if (data.available_lots !== undefined) {
      this.animateCounterUpdate('available-lots', data.available_lots);
    }
    
    if (data.available_wafers !== undefined) {
      this.animateCounterUpdate('available-wafers', data.available_wafers);
    }
    
    if (data.shipped_wafers !== undefined) {
      this.animateCounterUpdate('shipped-wafers', data.shipped_wafers);
    }

    // Update progress bars
    if (data.capacity_percentage !== undefined) {
      this.updateProgressBar('capacity', data.capacity_percentage);
    }

    // Show notification for significant changes
    if (data.change_type && data.change_type === 'significant') {
      this.showUpdateNotification('inventory', data.message || 'Inventory updated');
    }

    // Update last update timestamp
    this.updateLastRefreshTime();
  }

  handleShipmentUpdate(data) {
    console.log('🚚 Shipment update received:', data);
    
    // Update shipment-related metrics
    if (data.active_shipments !== undefined) {
      this.animateCounterUpdate('active-shipments', data.active_shipments);
    }

    // Update shipment status indicators
    if (data.status_updates) {
      this.updateShipmentStatuses(data.status_updates);
    }

    // Show notification for new shipments
    if (data.new_shipment) {
      this.showUpdateNotification('shipment', `New shipment: ${data.new_shipment.name}`);
    }
  }

  handleSystemNotification(data) {
    console.log('🔔 System notification received:', data);
    
    // Show system-wide notifications
    this.showSystemAlert(data.type, data.message, data.priority);
    
    // Update notification bell
    this.updateNotificationBell(data.unread_count);
  }

  updateDashboardMetrics(data) {
    console.log('📊 Dashboard metrics update:', data);
    
    // Batch update all metrics to avoid UI flickering
    this.batchUpdateMetrics(data.metrics);
    
    // Update charts if data provided
    if (data.chart_data) {
      this.updateCharts(data.chart_data);
    }
  }

  animateCounterUpdate(elementId, newValue) {
    const element = document.querySelector(`[data-stat="${elementId}"]`);
    if (!element) return;

    const currentValue = parseInt(element.textContent) || 0;
    const difference = newValue - currentValue;
    
    if (difference === 0) return;

    // Add visual feedback for changes
    element.classList.add('updating');
    
    // Animate the counter
    this.animateValue(element, currentValue, newValue, 1000);
    
    // Show change indicator
    this.showChangeIndicator(element, difference);
    
    setTimeout(() => {
      element.classList.remove('updating');
    }, 1000);
  }

  animateValue(element, start, end, duration) {
    const startTime = performance.now();
    const change = end - start;

    const updateValue = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Use easing function for smooth animation
      const easedProgress = this.easeOutCubic(progress);
      const currentValue = Math.round(start + (change * easedProgress));
      
      element.textContent = currentValue;
      
      if (progress < 1) {
        requestAnimationFrame(updateValue);
      }
    };
    
    requestAnimationFrame(updateValue);
  }

  easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
  }

  showChangeIndicator(element, difference) {
    const indicator = document.createElement('span');
    indicator.className = `change-indicator ${difference > 0 ? 'positive' : 'negative'}`;
    indicator.textContent = `${difference > 0 ? '+' : ''}${difference}`;
    
    element.parentNode.appendChild(indicator);
    
    // Animate and remove
    setTimeout(() => {
      indicator.classList.add('fade-out');
      setTimeout(() => indicator.remove(), 300);
    }, 2000);
  }

  updateProgressBar(type, percentage) {
    const progressBar = document.querySelector(`.progress-bar-fill.gradient-${type === 'capacity' ? 'blue' : 'green'}`);
    if (progressBar) {
      progressBar.style.width = `${Math.min(percentage, 100)}%`;
      progressBar.classList.add('updating');
      setTimeout(() => progressBar.classList.remove('updating'), 500);
    }
  }

  showUpdateNotification(type, message) {
    // Use SweetAlert2 for elegant notifications
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Live Update',
        text: message,
        icon: 'info',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'real-time-notification'
        }
      });
    }
  }

  showConnectionStatus(status) {
    const statusElement = document.getElementById('connection-status');
    if (statusElement) {
      statusElement.className = `connection-status ${status}`;
      statusElement.textContent = status === 'connected' ? '🟢 Live' : 
                                 status === 'disconnected' ? '🔴 Offline' : '🟡 Error';
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connectSocket();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  startPeriodicUpdates() {
    // Fallback periodic updates every 30 seconds
    setInterval(() => {
      if (!this.isConnected) {
        this.fetchDashboardData();
      }
    }, 30000);
  }

  async fetchDashboardData() {
    try {
      const response = await fetch('/api/dashboard/stats');
      const data = await response.json();
      
      if (data.success) {
        this.updateDashboardMetrics(data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    }
  }

  setupVisibilityChangeHandler() {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && this.isConnected) {
        // Request fresh data when tab becomes visible
        this.requestInitialData();
      }
    });
  }

  updateLastRefreshTime() {
    this.lastUpdateTime = new Date();
    const timeElement = document.getElementById('last-update-time');
    if (timeElement) {
      timeElement.textContent = `Last updated: ${this.lastUpdateTime.toLocaleTimeString()}`;
    }
  }

  setupEventListeners() {
    // Manual refresh button
    const refreshBtn = document.getElementById('manual-refresh');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.requestInitialData();
        this.showUpdateNotification('system', 'Dashboard refreshed manually');
      });
    }
  }
}

// Initialize real-time dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  if (window.location.pathname === '/' || window.location.pathname === '/dashboard') {
    window.realTimeDashboard = new RealTimeDashboard();
  }
});

// Export for use in other modules
window.RealTimeDashboard = RealTimeDashboard;
