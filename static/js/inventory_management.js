// Inventory Management Script

// State Variables
let currentPage = 1;
let pageSize = 10;
let totalItems = 0;

// Check user permissions on load
document.addEventListener("DOMContentLoaded", function () {
  setupPermissionBasedButtons();

  // Check if we just completed a modification and need to force a fresh search
  const justModified = sessionStorage.getItem('justModifiedInventory');
  if (justModified === 'true') {
    // Clear the flag
    sessionStorage.removeItem('justModifiedInventory');

    // Show message to indicate data is being refreshed
    showMessage("Refreshing data after update...", "info");

    // Force a search with cache-busting
    setTimeout(() => {
      // Add a random parameter to force a complete refresh
      currentPage = 1;
      searchInventory(true); // Pass true to indicate a forced refresh
    }, 300);
  }
});

// Setup buttons based on user role/permissions
function setupPermissionBasedButtons() {
  const userRole = document.body.dataset.userRole;
  const isAdmin = userRole === "admin";

  // Get all permission-restricted buttons
  const permissionButtons = document.querySelectorAll("[data-permission]");

  permissionButtons.forEach((button) => {
    const permission = button.dataset.permission;
    // Admin has all permissions, others have none for these operations
    if (!isAdmin) {
      // Add click handler to show message instead of actual function
      button.addEventListener(
        "click",
        function (e) {
          e.preventDefault();
          e.stopPropagation();
          showMessage(
            `Permission required: You need the '${permission}' permission to use this feature.`,
            "error"
          );
        },
        true
      );
      // Update button appearance
      button.classList.add("opacity-50", "cursor-not-allowed");
    }
  });
}

// Initialize global objects
const currentSort = {
  field: "arrived_at",
  direction: "desc",
};

const ENDPOINTS = {
  SEARCH: "/api/inventory/search",
  ADD: "/api/inventory/add",
  MODIFY: "/api/inventory/modify",
  BULK_MODIFY: "/api/inventory/bulk-modify",
  DELETE: "/api/inventory/delete",
  SYNC: "/api/inventory/sync-wafers",
  EXPORT: "/api/inventory/export",
  CORRECT_WAFER: "/api/inventory/correct-wafer",
  UPDATE_MODULES: "/api/inventory/update_modules",
  HISTORY: "/api/inventory/history/",
};

// Add event listeners to search fields to trigger search on Enter key
document.addEventListener('DOMContentLoaded', function() {
  // Get all input fields in the search area
  const searchInputs = document.querySelectorAll('.bg-white.dark\\:bg-gray-800.rounded-lg.shadow.p-6.mb-6 input, .bg-white.dark\\:bg-gray-800.rounded-lg.shadow.p-6.mb-6 select');

  // Add event listener to each input field
  searchInputs.forEach(input => {
    input.addEventListener('keypress', function(event) {
      // Check if the key pressed was Enter
      if (event.key === 'Enter') {
        // Prevent the default form submission
        event.preventDefault();
        // Trigger the search
        searchInventory();
      }
    });
  });
});

// Counter for selected items
let selectedCount = 0;

function updateSelectedCount() {
  selectedCount = document.querySelectorAll(
    'input[name="inventory-item"]:checked'
  ).length;
  document.getElementById("selected-count").textContent = selectedCount;
}

// Select All Functionality
document.getElementById("select-all")?.addEventListener("change", function () {
  const checkboxes = document.querySelectorAll(
    '#inventory-table-body input[name="inventory-item"]'
  );
  checkboxes.forEach((checkbox) => {
    checkbox.checked = this.checked;
  });
  updateSelectedCount();
});

// Utility function to get the CSRF token
function getCSRFToken() {
  // Try to get from cookie
  const tokenMatch = document.cookie.match(/csrftoken=([^;]+)/);
  if (tokenMatch) {
    return tokenMatch[1];
  }

  // If not found in cookie, try to get from the meta tag
  const csrfInput = document.querySelector('meta[name="csrf-token"]');
  if (csrfInput) {
    return csrfInput.getAttribute("content");
  }

  // If still not found, try for Flask CSRF input
  const flaskCsrf = document.querySelector('input[name="csrf_token"]');
  if (flaskCsrf) {
    return flaskCsrf.value;
  }

  console.error("CSRF token not found");
  return "";
}

// UI Helper Functions
function showMessage(message, type = "info") {
  console.log(`Showing message: ${message} (${type})`);

  // If SweetAlert2 is available, use it for a better UI experience
  if (typeof Swal !== 'undefined') {
    Swal.fire({
      title: type.charAt(0).toUpperCase() + type.slice(1),
      text: message,
      icon: type === 'error' ? 'error' : type === 'success' ? 'success' : 'info',
      toast: true,
      position: 'bottom-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true
    });
    return;
  }

  // Fallback to simple message overlay if SweetAlert2 is not available
  const existingMessage = document.querySelector(".message-overlay");
  if (existingMessage) {
    existingMessage.remove();
  }

  const messageElement = document.createElement("div");
  messageElement.className = `message-overlay fixed bottom-0 right-0 m-4 p-4 rounded-lg shadow-lg z-50 ${
    type === "error"
      ? "bg-red-500 text-white"
      : type === "success"
      ? "bg-green-500 text-white"
      : "bg-blue-500 text-white"
  }`;
  messageElement.textContent = message;
  document.body.appendChild(messageElement);

  // Remove message after 3 seconds
  setTimeout(() => messageElement.remove(), 3000);
}

// Form Handling
function getSearchParams() {
  const params = {
    wafer_id: getInputValue("wafer-id").trim(),
    lot_id: getInputValue("lot-id").trim(),
    cassette_id: getInputValue("cassette-id").trim(),
    location_id: getInputValue("location-id").trim(),
    xfab_id: getInputValue("xfab-id").trim(),
    mask_set_id: getInputValue("mask-set-id").trim(),
    module_name: getInputValue("module-name").trim(),
    arrived_at_from: getInputValue("arrived-at-from"),
    arrived_at_to: getInputValue("arrived-at-to"),
    sent_at_from: getInputValue("sent-at-from"),
    sent_at_to: getInputValue("sent-at-to"),
    page: currentPage,
    page_size: pageSize,
    sort_field: currentSort.field,
    sort_direction: currentSort.direction,
  };

  const slotId = getInputValue("slot-id");
  if (slotId) {
    params.slot_id = parseInt(slotId);
  }

  // Log the search parameters for debugging
  console.log("Search Parameters:", params);

  return params;
}

function handleSort(field) {
  // If clicking the same field, toggle direction
  if (currentSort.field === field) {
    currentSort.direction = currentSort.direction === "asc" ? "desc" : "asc";
  } else {
    // New field, set to ascending
    currentSort.field = field;
    currentSort.direction = "asc";
  }

  // Update sort indicators
  updateSortIndicators();

  // Reset to first page when sorting
  currentPage = 1;

  // Refresh data with new sort
  searchInventory();
}
// Sort Indicators Management

function updateSortIndicators() {
  // Remove all existing indicators
  document.querySelectorAll(".sort-indicator").forEach((el) => {
    el.classList.remove("asc", "desc");
    el.textContent = "";
  });

  // Add indicator for current sort
  if (currentSort.field) {
    const indicator = document.getElementById(`sort-${currentSort.field}`);
    if (indicator) {
      indicator.textContent = currentSort.direction === "asc" ? "↑" : "↓";
    }
  }
}

// pagination event handlers

function initializePagination() {
  const pageSizeSelect = document.getElementById("page-size");
  const prevPageBtn = document.getElementById("prev-page");
  const nextPageBtn = document.getElementById("next-page");

  pageSizeSelect.value = pageSize;

  pageSizeSelect.addEventListener("change", function () {
    pageSize = parseInt(this.value);
    currentPage = 1; // Reset to first page when changing page size
    searchInventory();
  });

  prevPageBtn.addEventListener("click", function () {
    if (currentPage > 1) {
      currentPage--;
      searchInventory();
    }
  });

  nextPageBtn.addEventListener("click", function () {
    if (currentPage * pageSize < totalItems) {
      currentPage++;
      searchInventory();
    }
  });
}
// Search Functionality and Validation Functions
function validateSearchParams(params) {
  const hasValue = Object.values(params).some(
    (value) => value !== undefined && value !== null && value !== ""
  );

  if (!hasValue) {
    throw new Error("Please enter at least one search criteria");
  }

  if (params.slot_id && (isNaN(params.slot_id) || params.slot_id < 0)) {
    throw new Error("Invalid slot ID");
  }

  // Validate date ranges
  if (params.arrived_at_from && params.arrived_at_to) {
    if (new Date(params.arrived_at_from) > new Date(params.arrived_at_to)) {
      throw new Error("Invalid arrival date range");
    }
  }

  if (params.sent_at_from && params.sent_at_to) {
    if (new Date(params.sent_at_from) > new Date(params.sent_at_to)) {
      throw new Error("Invalid sent date range");
    }
  }

  return true;
}

// Enhanced search function with progress indicator
function searchInventory(forceRefresh = false) {
  try {
    // Initialize progress indicator
    const progress = window.progressIndicator || new ProgressIndicator();
    const steps = [
      'Validating search parameters',
      'Querying database',
      'Processing results',
      'Updating table display'
    ];
    progress.init('Searching Inventory', steps);

    // Step 1: Validate parameters
    progress.updateProgress(0);
    const params = getSearchParams();

    if (forceRefresh) {
      params.cache_buster = Date.now();
    }

    validateSearchParams(params);
    progress.nextStep();

    // Step 2: Make API request
    progress.updateProgress(1, 'Querying database...');

    fetch(ENDPOINTS.SEARCH, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCSRFToken(),
        "Cache-Control": forceRefresh ? "no-cache" : "default",
      },
      body: JSON.stringify(params),
    })
    .then(response => {
      progress.nextStep('Processing server response...');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      progress.nextStep('Updating display...');

      if (data.success) {
        updateTable(data.data, data.pagination);
        updateSortIndicators();

        // Show success message
        const itemCount = data.pagination ? data.pagination.total : data.data.length;
        showMessage(`Found ${itemCount} inventory items`, "success");

        progress.complete(`Found ${itemCount} items`);
      } else {
        throw new Error(data.message || "Search failed");
      }
    })
    .catch(error => {
      console.error("Search error:", error);
      progress.error(`Search failed: ${error.message}`);
      showMessage(`Search failed: ${error.message}`, "error");
    });

  } catch (error) {
    console.error("Search validation error:", error);
    showMessage(error.message, "error");
  }
}

// Table Management
function updateTable(data, pagination) {
  const tableBody = document.getElementById("inventory-table-body");
  if (!tableBody) {
    console.error("Table body element not found!");
    return;
  }

  try {
    // Clear the table body completely
    tableBody.innerHTML = "";

    if (!data || data.length === 0) {
      tableBody.innerHTML = `
                <tr>
                    <td colspan="12" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        No inventory items found
                    </td>
                </tr>`;
      updatePaginationControls(0);
      return;
    }

    // For each item, create a row
    data.forEach((item) => {
      // Force date reprocessing to avoid caching - preserve original date strings
      // Don't modify the original data, let formatDate handle the conversion
      console.log(`Processing item ${item.wafer_id}: arrived_at=${item.arrived_at}, sent_at=${item.sent_at}`);

      // Create the row using innerHTML for simplicity, but with unique timestamps
      const row = document.createElement("tr");
      row.className = "hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 ease-in-out";
      row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <input type="checkbox" name="inventory-item" value="${
                      item.wafer_id || ""
                    }"
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring"
                           onchange="updateSelectedCount()">
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">${
                  item.wafer_id || ""
                }</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">${
                  item.lot_id || ""
                }</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">${
                  item.xfab_id || ""
                }</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">${
                  item.mask_set_id || ""
                }</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">${
                  item.module_name || ""
                }</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">${
                  item.cassette_id || ""
                }</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">${
                  item.slot_id || ""
                }</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">${
                  item.location_label || item.location_id || ""
                }</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200 date-cell" data-date-type="arrived" data-wafer-id="${item.wafer_id}" data-timestamp="${Date.now()}" data-random="${Math.random()}" data-original-date="${item.arrived_at}" title="Arrived: ${item.arrived_at ? formatDate(item.arrived_at) : 'None'}">${formatDate(
                  item.arrived_at
                )}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200 date-cell" data-date-type="sent" data-wafer-id="${item.wafer_id}" data-timestamp="${Date.now()}" data-random="${Math.random()}" data-original-date="${item.sent_at}" title="Sent: ${item.sent_at ? formatDate(item.sent_at) : 'None'}">${formatDate(
                  item.sent_at
                )}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                    <div class="flex items-center space-x-2">
                        <button onclick="showWaferHistory('${item.wafer_id}')"
                                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                title="View History">
                            <i class="fas fa-history"></i>
                        </button>
                        <button onclick="InventoryModals.showWaferCorrectionModal('${
                          item.wafer_id
                        }')"
                                class="text-yellow-600 hover:text-yellow-800 dark:text-yellow-400 dark:hover:text-yellow-300"
                                title="Correct Wafer ID">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </td>
            `;

      tableBody.appendChild(row);
    });

    // Update pagination controls and show pagination info
    updatePaginationControls(pagination ? pagination.total : data.length);

    // Show pagination info when there are results
    const paginationInfo = document.getElementById('pagination-info');
    if (paginationInfo) {
      paginationInfo.style.display = 'block';
    }

    // After table is updated, add listeners for date cells to force refresh on click
    document.querySelectorAll('.date-cell').forEach(cell => {
      cell.addEventListener('click', () => {
        // Add refresh animation
        cell.classList.add('date-refreshing', 'bg-blue-100', 'border-2', 'border-blue-300');

        // Get the original date and re-format it
        const originalDate = cell.getAttribute('data-original-date');
        const dateType = cell.getAttribute('data-date-type');
        const waferId = cell.getAttribute('data-wafer-id');

        console.log(`Refreshing ${dateType} date for wafer ${waferId}: ${originalDate}`);

        // Re-format and update the cell content
        if (originalDate) {
          const newFormattedDate = formatDate(originalDate);
          cell.innerHTML = newFormattedDate;
          console.log(`Updated ${dateType} date display to: ${newFormattedDate}`);
        }

        // Show a brief "refreshed" indicator
        const originalTitle = cell.title;
        cell.title = `✓ Refreshed: ${originalDate}`;

        setTimeout(() => {
          cell.classList.remove('date-refreshing', 'bg-blue-100', 'border-2', 'border-blue-300');
          cell.title = originalTitle;
        }, 1000);
      });
    });
  } catch (error) {
    console.error("Error updating table:", error);
    showMessage("Error updating table: " + error.message, "error");
  }
}

// Pagination Controls

function updatePaginationControls(total) {
  totalItems = total;
  const start = total > 0 ? (currentPage - 1) * pageSize + 1 : 0;
  const end = Math.min(start + pageSize - 1, total);

  document.getElementById("page-start").textContent = start;
  document.getElementById("page-end").textContent = end;
  document.getElementById("total-items").textContent = total;

  const prevBtn = document.getElementById("prev-page");
  const nextBtn = document.getElementById("next-page");

  // Update button states
  prevBtn.disabled = currentPage <= 1;
  nextBtn.disabled = end >= total;

  // Update the appearance of disabled buttons
  if (prevBtn.disabled) {
    prevBtn.classList.add("opacity-50", "cursor-not-allowed");
  } else {
    prevBtn.classList.remove("opacity-50", "cursor-not-allowed");
  }

  if (nextBtn.disabled) {
    nextBtn.classList.add("opacity-50", "cursor-not-allowed");
  } else {
    nextBtn.classList.remove("opacity-50", "cursor-not-allowed");
  }
}

// Format date for display
function formatDate(date) {
  if (!date) return "";

  try {
    // Log the input date for debugging
    console.log("Formatting date:", date, typeof date);

    // Simple approach - extract the date part for consistent display
    if (typeof date === "string") {
      // For ISO format dates, just take the date part
      if (date.includes('T')) {
        const datePart = date.split('T')[0];
        console.log("Extracted date part:", datePart);
        return datePart;
      }
      
      // For YYYY-MM-DD format, return as is
      if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
        console.log("Already in YYYY-MM-DD format:", date);
        return date;
      }
    }
    
    // For other formats, try Date object
    let dateObj;
    if (typeof date === "string") {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      console.warn("Unsupported date type:", typeof date, date);
      return "";
    }

    // Validate the date
    if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
      console.warn("Invalid date after parsing:", dateObj);
      return "";
    }

    // Format as YYYY-MM-DD without timezone consideration
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');

    const formatted = `${year}-${month}-${day}`;
    console.log("Final formatted date:", formatted);
    return formatted;
  } catch (e) {
    console.error("Error formatting date:", e, date);
    // Last-resort fallback: try to extract date portion from string
    if (typeof date === "string") {
      if (date.includes('T')) {
        return date.split('T')[0];
      }
      if (date.match(/^\d{4}-\d{2}-\d{2}/)) {
        return date.substring(0, 10);
      }
    }
    return "";
  }
}

function getInputValue(id) {
  return document.getElementById(id)?.value || "";
}

// Reset the form to default state
function clearForm() {
  const fields = [
    "wafer-id",
    "lot-id",
    "xfab-id",
    "mask-set-id",
    "module-name",
    "cassette-id",
    "slot-id",
    "location-id",
    "arrived-at-from",
    "arrived-at-to",
    "sent-at-from",
    "sent-at-to",
  ];

  fields.forEach((id) => {
    const element = document.getElementById(id);
    if (element) {
      element.value = "";
    }
  });

  // Reset to default sort
  currentSort.field = "arrived_at";
  currentSort.direction = "desc";
  updateSortIndicators();

  // Reset pagination
  currentPage = 1;
}

// Missing function implementations
// Function to show wafer history modal
function showWaferHistory(waferId) {
  if (!waferId) {
    showMessage("No wafer ID provided", "error");
    return;
  }

  console.log(`Showing history for wafer: ${waferId}`);

  // Create a loading modal
  const modalHTML = `
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="history-modal">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Wafer History: ${waferId}</h2>
            <button class="text-gray-400 hover:text-gray-500" id="close-history-modal">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div id="history-content" class="mt-4">
            <div class="flex justify-center items-center py-8">
              <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              <p class="ml-2">Loading history...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  const modalContainer = document.createElement('div');
  modalContainer.innerHTML = modalHTML;
  document.body.appendChild(modalContainer);

  // Set up close button
  document.getElementById('close-history-modal').addEventListener('click', () => {
    modalContainer.remove();
  });

  // Fetch wafer history
  fetch(`/api/inventory/history/${waferId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    const historyContent = document.getElementById('history-content');

    if (!data.success || !data.history || data.history.length === 0) {
      historyContent.innerHTML = `<p class="text-center text-gray-500 py-4">No history available for this wafer.</p>`;
      return;
    }

    // Render history in a table
    let historyHTML = `
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Action</th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Changed By</th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Details</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
    `;

    data.history.forEach(item => {
      const timestamp = new Date(item.timestamp).toLocaleString();

      // Format changes in a user-friendly way
      let changesHtml = '';
      if (item.changes) {
        const changes = item.changes;
        changesHtml = '<ul class="list-disc pl-4">';

        // Handle different change types
        if (changes.old_values && changes.new_values) {
          // Compare old and new values
          Object.keys(changes.new_values).forEach(key => {
            const oldValue = changes.old_values[key] !== undefined ? changes.old_values[key] : 'None';
            const newValue = changes.new_values[key] !== undefined ? changes.new_values[key] : 'None';

            // Skip if values are identical or internal fields
            if (oldValue === newValue || key.startsWith('_')) return;

            // Format field name for display
            const fieldName = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            // Format date fields
            const isDateField = key.includes('_at') || key.includes('date');
            let displayOldValue = isDateField && oldValue ? formatDate(oldValue) : oldValue;
            let displayNewValue = isDateField && newValue ? formatDate(newValue) : newValue;

            changesHtml += `<li><strong>${fieldName}:</strong> changed from "${displayOldValue}" to "${displayNewValue}"</li>`;
          });
        } else if (typeof changes === 'string') {
          // Handle string change descriptions
          changesHtml += `<li>${changes}</li>`;
        } else if (typeof changes === 'object') {
          // Handle flat object of changes
          Object.entries(changes).forEach(([key, value]) => {
            if (key === 'action' || key === 'timestamp' || key === 'user') return;

            const fieldName = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            changesHtml += `<li><strong>${fieldName}:</strong> ${value}</li>`;
          });
        }

        changesHtml += '</ul>';

        // If no specific changes were found, show a generic message
        if (changesHtml === '<ul class="list-disc pl-4"></ul>') {
          changesHtml = '<p>Record was updated</p>';
        }
      } else {
        changesHtml = '<p>No detailed change information available</p>';
      }

      historyHTML += `
        <tr>
          <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${timestamp}</td>
          <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">${item.action || 'Update'}</td>
          <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${item.user || 'System'}</td>
          <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
            <div class="max-h-20 overflow-y-auto">
              ${changesHtml}
            </div>
          </td>
        </tr>
      `;
    });

    historyHTML += `
          </tbody>
        </table>
      </div>
    `;

    historyContent.innerHTML = historyHTML;
  })
  .catch(error => {
    console.error('Error fetching wafer history:', error);
    document.getElementById('history-content').innerHTML = `
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <p>Error loading history: ${error.message}</p>
      </div>
    `;
  });
}

// Function to sync wafers
function syncWafersToInventory() {
  console.log("Syncing wafers to inventory...");

  // Show loading overlay
  const loadingOverlay = document.createElement('div');
  loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
  loadingOverlay.innerHTML = `
    <div class="bg-white p-4 rounded-lg shadow-md">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
      <p class="mt-2 text-gray-700">Syncing wafers to inventory...</p>
    </div>
  `;
  document.body.appendChild(loadingOverlay);

  // Call sync API
  fetch('/api/inventory/sync-wafers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': getCSRFToken(),
      'X-Requested-With': 'XMLHttpRequest'
    },
  })
  .then(response => response.json())
  .then(data => {
    loadingOverlay.remove();

    if (data.success) {
      // Use SweetAlert2 to show a more detailed response
      Swal.fire({
        title: 'Sync Completed',
        icon: 'success',
        html: `
          <div class="text-left">
            <p>${data.message || "Sync completed successfully"}</p>
            <div class="mt-3 p-2 bg-gray-100 rounded">
              <p><strong>Summary:</strong></p>
              <ul class="list-disc pl-5">
                <li>Total wafers: ${data.total_wafers || 0}</li>
                <li>Wafers in inventory: ${data.existing_wafers || 0}</li>
                <li>Newly added: ${data.added || 0}</li>
                <li>Skipped: ${data.skipped || 0}</li>
                <li>Errors: ${data.errors || 0}</li>
              </ul>
            </div>
          </div>
        `,
        confirmButtonText: 'OK',
        confirmButtonColor: '#3085d6'
      }).then(() => {
        // Force refresh inventory data after sync
        forceRefreshInventoryData();
      });
    } else {
      Swal.fire({
        title: 'Sync Failed',
        text: data.message || "Failed to sync wafers",
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#3085d6'
      });
    }
  })
  .catch(error => {
    loadingOverlay.remove();
    console.error("Error syncing wafers:", error);
    Swal.fire({
      title: 'Error',
      text: error.message || "An unexpected error occurred during sync",
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#3085d6'
    });
  });
}

// Add inventory function
function addInventory() {
  console.log("Adding inventory...");

  // Create modal for adding inventory
  const modalHTML = `
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="add-inventory-modal">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Add Wafers to Inventory</h2>
            <button class="text-gray-400 hover:text-gray-500" id="close-add-modal">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <form id="add-inventory-form" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Left column - Wafer IDs & Meta -->
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Wafer IDs (one per line)</label>
                  <textarea id="add-wafer-ids" rows="5" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm" required placeholder="Enter wafer IDs, one per line"></textarea>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">LGT Lot ID</label>
                  <input type="text" id="add-lot-id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm" required placeholder="Enter LGT Lot ID">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">XFAB ID</label>
                  <input type="text" id="add-xfab-id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm" required placeholder="Enter XFAB ID">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Mask Set ID</label>
                  <input type="text" id="add-mask-set-id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm" required placeholder="Enter Mask Set ID">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Module Name</label>
                  <input type="text" id="add-module-name" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm" placeholder="Enter Module Name">
                </div>
              </div>

              <!-- Right column - Location & Slots -->
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Slot Assignment</label>
                  <select id="slot-assignment-type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm">
                    <option value="auto">Auto-assign slots (1, 2, 3...)</option>
                    <option value="manual">Manual slot assignment</option>
                  </select>
                </div>

                <div id="slot-ids-container">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Slot IDs (one per line, matching wafer IDs)</label>
                  <textarea id="add-slot-ids" rows="5" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm" placeholder="Enter slot IDs, one per line"></textarea>
                  <button type="button" id="generate-slots-btn" class="mt-2 bg-blue-100 text-blue-700 py-1 px-2 rounded text-sm hover:bg-blue-200">
                    <i class="fas fa-sync-alt mr-1"></i>Generate Slots
                  </button>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cassette ID</label>
                  <input type="text" id="add-cassette-id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm" required placeholder="Enter Cassette ID">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Location</label>
                  <select id="add-location-id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm" required>
                    <option value="">Select Location</option>
                    <!-- Will be populated dynamically -->
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Arrived At</label>
                  <input type="datetime-local" id="add-arrived-at" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sent At (optional)</label>
                  <input type="datetime-local" id="add-sent-at" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm">
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
              <button type="button" id="add-cancel-btn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                Cancel
              </button>
              <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Add to Inventory
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `;

  const modalContainer = document.createElement('div');
  modalContainer.innerHTML = modalHTML;
  document.body.appendChild(modalContainer);

  // Set default arrival date to now
  const now = new Date();
  const formattedNow = now.toISOString().slice(0, 16);
  document.getElementById('add-arrived-at').value = formattedNow;

  // Set up close button
  document.getElementById('close-add-modal').addEventListener('click', () => {
    modalContainer.remove();
  });

  // Set up cancel button
  document.getElementById('add-cancel-btn').addEventListener('click', () => {
    modalContainer.remove();
  });

  // Generate slots button
  document.getElementById('generate-slots-btn').addEventListener('click', () => {
    const waferIds = document.getElementById('add-wafer-ids').value
      .split(/[\n,]/)
      .map(id => id.trim())
      .filter(id => id !== '');

    if (waferIds.length === 0) {
      showMessage("Please enter wafer IDs first", "error");
      return;
    }

    // Auto-generate sequential slot numbers
    const slotIds = waferIds.map((_, index) => index + 1);
    document.getElementById('add-slot-ids').value = slotIds.join('\n');
  });

  // Handle slot assignment type change
  document.getElementById('slot-assignment-type').addEventListener('change', function() {
    const container = document.getElementById('slot-ids-container');
    if (this.value === 'auto') {
      container.style.display = 'block';
    } else {
      container.style.display = 'block';
    }
  });

  // Load locations for dropdown
  fetch('/api/locations/list', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    const locationSelect = document.getElementById('add-location-id');

    if (data.success && data.locations) {
      data.locations.forEach(location => {
        const option = document.createElement('option');
        option.value = location.location_id;
        option.textContent = location.label || location.location_id;
        locationSelect.appendChild(option);
      });
    } else {
      showMessage("Failed to load locations", "error");
    }
  })
  .catch(error => {
    console.error("Error loading locations:", error);
  });

  // Form submission
  document.getElementById('add-inventory-form').addEventListener('submit', async (e) => {
    e.preventDefault();

    // Get form data
    const waferIds = document.getElementById('add-wafer-ids').value
      .split(/[\n,]/)
      .map(id => id.trim())
      .filter(id => id !== '');

    const slotIds = document.getElementById('add-slot-ids').value
      .split(/[\n,]/)
      .map(id => id.trim())
      .filter(id => id !== '')
      .map(id => parseInt(id));

    // Validation
    if (waferIds.length === 0) {
      showMessage("Please enter at least one wafer ID", "error");
      return;
    }

    if (waferIds.length !== slotIds.length) {
      showMessage("Number of wafer IDs must match number of slot IDs", "error");
      return;
    }

    // Create request data
    const requestData = {
      wafer_ids: waferIds,
      slot_ids: slotIds,
      lot_id: document.getElementById('add-lot-id').value.trim(),
      xfab_id: document.getElementById('add-xfab-id').value.trim(),
      mask_set_id: document.getElementById('add-mask-set-id').value.trim(),
      module_name: document.getElementById('add-module-name').value.trim(),
      cassette_id: document.getElementById('add-cassette-id').value.trim(),
      location_id: document.getElementById('add-location-id').value,
      arrived_at: document.getElementById('add-arrived-at').value,
      sent_at: document.getElementById('add-sent-at').value || null
    };

    // Show loading overlay
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    loadingOverlay.innerHTML = `
      <div class="bg-white p-4 rounded-lg shadow-md">
        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p class="mt-2 text-gray-700">Adding inventory...</p>
      </div>
    `;
    document.body.appendChild(loadingOverlay);

    try {
      // Call the API
      const response = await fetch('/api/inventory/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCSRFToken(),
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(requestData)
      });

      const data = await response.json();

      loadingOverlay.remove();

      if (data.success) {
        showMessage(data.message || "Successfully added to inventory", "success");
        modalContainer.remove();

        // Refresh the inventory data
        forceRefreshInventoryData();
      } else {
        // Check if this is a special error that requires a prominent alert
        if (data.show_alert === true && typeof Swal !== 'undefined') {
          // Use a prominent SweetAlert for critical errors like foreign key violations
          // Format the message for better readability
          const formattedMessage = (data.message || "Failed to add to inventory")
            .replace(/ERREUR:/g, '<strong>ERROR:</strong>')
            .replace(/fk_wafer_inventory_wafer_id_wafers/g, '<em>wafer_inventory → wafers</em>')
            .replace(/wafer_inventory/g, '<strong>wafer_inventory</strong>')
            .replace(/wafers/g, '<strong>wafers</strong>');

          Swal.fire({
            icon: 'error',
            title: 'Database Constraint Violation',
            html: `
              <div style="text-align: left; line-height: 1.6;">
                <p style="margin-bottom: 15px; font-size: 16px;">
                  <strong>Unable to add wafers to inventory</strong>
                </p>
                <div style="background-color: #fee; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #dc2626;">
                  <p style="margin: 0; font-size: 14px; color: #7f1d1d;">
                    ${formattedMessage}
                  </p>
                </div>
                <p style="margin-bottom: 10px; font-size: 14px;">
                  <strong>What this means:</strong>
                </p>
                <ul style="margin: 0; padding-left: 20px; font-size: 14px;">
                  <li>The wafer(s) you're trying to add don't exist in the main wafers table</li>
                  <li>Foreign key constraints prevent adding non-existent wafers to inventory</li>
                </ul>
                <p style="margin-top: 15px; margin-bottom: 0; font-size: 14px;">
                  <strong>Solution:</strong> Add the wafer(s) to the main wafers table first, then add them to inventory.
                </p>
              </div>
            `,
            confirmButtonText: 'I Understand',
            confirmButtonColor: '#dc2626',
            allowOutsideClick: false,
            allowEscapeKey: false,
            width: '600px',
            timer: 15000, // Auto-close after 15 seconds
            timerProgressBar: true,
            showCloseButton: true,
            customClass: {
              popup: 'text-left'
            }
          });
        } else {
          // Use regular showMessage for other errors
          showMessage(data.message || "Failed to add to inventory", "error");
        }
      }
    } catch (error) {
      loadingOverlay.remove();
      console.error("Error adding inventory:", error);
      showMessage(error.message || "An error occurred while adding inventory", "error");
    }
  });
}

// Object to hold modal functionality
const InventoryModals = {
  // Show wafer correction modal
  showWaferCorrectionModal: function(waferId) {
    console.log(`Showing correction modal for wafer: ${waferId}`);

    const modalHTML = `
      <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Correct Wafer ID
            </h3>

            <form id="wafer-correction-form">
              <div class="space-y-4">
                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Original Wafer ID</label>
                  <input type="text" id="old-wafer-id" value="${waferId}" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3" readonly>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">New Wafer ID</label>
                  <input type="text" id="new-wafer-id" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3" required>
                  <p class="mt-1 text-sm text-gray-500">Enter the corrected wafer ID</p>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Correction Type</label>
                  <select id="correction-type" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3">
                    <option value="typo">Typo Correction</option>
                    <option value="replacement">Wafer Replacement</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Correction Reason</label>
                  <textarea id="correction-reason" rows="3" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3" required></textarea>
                </div>

                <div class="mt-5 flex justify-end gap-3">
                  <button type="button" id="correction-cancel-btn"
                          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                    Cancel
                  </button>
                  <button type="submit"
                          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Apply Correction
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    `;

    const modalElement = document.createElement("div");
    modalElement.innerHTML = modalHTML;
    document.body.appendChild(modalElement);

    // Handle cancel button
    document.getElementById('correction-cancel-btn').addEventListener('click', () => {
      modalElement.remove();
    });

    // Handle form submission
    document.getElementById('wafer-correction-form').addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = {
        old_wafer_id: document.getElementById('old-wafer-id').value,
        new_wafer_id: document.getElementById('new-wafer-id').value,
        correction_type: document.getElementById('correction-type').value,
        reason: document.getElementById('correction-reason').value
      };

      try {
        const response = await fetch('/api/inventory/correct-wafer', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken()
          },
          body: JSON.stringify(formData)
        });

        const data = await response.json();

        if (data.success) {
          showMessage(data.message || "Wafer ID corrected successfully", "success");
          modalElement.remove();
          forceRefreshInventoryData();
        } else {
          throw new Error(data.message || "Failed to correct wafer ID");
        }
      } catch (error) {
        console.error("Error correcting wafer ID:", error);
        showMessage(error.message || "Failed to correct wafer ID", "error");
      }
    });
  }
};

// Action Functions
function modifyInventory() {
  // Get selected wafer IDs
  const selectedCheckboxes = document.querySelectorAll('input[name="inventory-item"]:checked');
  const selectedWaferIds = Array.from(selectedCheckboxes).map(cb => cb.value);

  if (selectedWaferIds.length === 0) {
    showMessage("Please select at least one wafer to modify", "error");
    return;
  }

  // Get the selected wafer data from the table rows
  const selectedWaferData = [];
  selectedCheckboxes.forEach(checkbox => {
    const row = checkbox.closest('tr');
    if (row) {
      // Create an object with the selected wafer's current data
      const waferData = {
        wafer_id: checkbox.value,
        lot_id: row.cells[2].textContent.trim(),
        xfab_id: row.cells[3].textContent.trim(),
        mask_set_id: row.cells[4].textContent.trim(),
        module_name: row.cells[5].textContent.trim(),
        cassette_id: row.cells[6].textContent.trim(),
        slot_id: row.cells[7].textContent.trim(),
        location_id: row.cells[8].textContent.trim(),
        location_label: row.cells[8].textContent.trim(),
        arrived_at: row.cells[9].textContent.trim(),
        sent_at: row.cells[10].textContent.trim()
      };
      selectedWaferData.push(waferData);
    }
  });

  // Call show modification modal with both IDs and data
  showModificationModal(selectedWaferIds, selectedWaferData);
}

// Analyze wafer data for bulk modification opportunities
function analyzeBulkModificationOpportunities(waferData) {
  if (!waferData || waferData.length <= 1) {
    return { hasGroups: false, groups: [] };
  }

  // Group wafers by common attributes
  const groups = {};

  waferData.forEach(wafer => {
    // Create a key based on common attributes that are typically modified together
    const groupKey = `${wafer.lot_id}|${wafer.module_name}|${wafer.mask_set_id}|${wafer.xfab_id}`;

    if (!groups[groupKey]) {
      groups[groupKey] = {
        lot_id: wafer.lot_id,
        module_name: wafer.module_name,
        mask_set_id: wafer.mask_set_id,
        xfab_id: wafer.xfab_id,
        wafers: [],
        count: 0
      };
    }

    groups[groupKey].wafers.push(wafer);
    groups[groupKey].count++;
  });

  // Filter groups that have more than one wafer (bulk opportunities)
  const bulkGroups = Object.values(groups).filter(group => group.count > 1);

  return {
    hasGroups: bulkGroups.length > 0,
    groups: bulkGroups,
    totalWafers: waferData.length,
    groupedWafers: bulkGroups.reduce((sum, group) => sum + group.count, 0)
  };
}

// Create bulk modification section HTML
function createBulkModificationSection(analysis) {
  if (!analysis.hasGroups) return '';

  const groupsHtml = analysis.groups.map((group, index) => `
    <div class="bulk-group border border-blue-200 rounded-lg p-3 mb-3 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center space-x-3">
          <input type="checkbox" id="bulk-group-${index}" class="bulk-group-checkbox rounded border-gray-300 text-blue-600"
                 data-group-index="${index}">
          <label for="bulk-group-${index}" class="font-medium text-gray-900 dark:text-white">
            Group ${index + 1} (${group.count} wafers)
          </label>
        </div>
        <button type="button" class="text-blue-600 hover:text-blue-800 text-sm"
                onclick="toggleGroupDetails(${index})">
          <i class="fas fa-chevron-down" id="group-chevron-${index}"></i> Details
        </button>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
        <div><span class="font-medium">LGT Lot ID:</span> ${group.lot_id || 'N/A'}</div>
        <div><span class="font-medium">Module:</span> ${group.module_name || 'N/A'}</div>
        <div><span class="font-medium">Mask Set:</span> ${group.mask_set_id || 'N/A'}</div>
        <div><span class="font-medium">XFAB ID:</span> ${group.xfab_id || 'N/A'}</div>
      </div>

      <div id="group-details-${index}" class="hidden mt-3 pt-3 border-t border-blue-300 dark:border-blue-600">
        <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">Wafers in this group:</div>
        <div class="flex flex-wrap gap-1">
          ${group.wafers.map(wafer => `
            <span class="inline-block bg-white dark:bg-gray-700 px-2 py-1 rounded text-xs border">
              ${wafer.wafer_id}
            </span>
          `).join('')}
        </div>
      </div>
    </div>
  `).join('');

  return `
    <div class="bulk-modification-section mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-lg border border-blue-200 dark:border-blue-700">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h4 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-layer-group mr-2 text-blue-600"></i>
            Bulk Modification Opportunities
          </h4>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Found ${analysis.groups.length} group(s) with common attributes (${analysis.groupedWafers}/${analysis.totalWafers} wafers)
          </p>
        </div>
        <div class="flex space-x-2">
          <button type="button" id="select-all-groups" class="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
            Select All Groups
          </button>
          <button type="button" id="apply-bulk-changes" class="text-sm bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700" disabled>
            Apply to Selected Groups
          </button>
        </div>
      </div>

      <div class="bulk-groups-container max-h-60 overflow-y-auto">
        ${groupsHtml}
      </div>

      <div class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded">
        <div class="flex items-start space-x-2">
          <i class="fas fa-lightbulb text-yellow-600 mt-0.5"></i>
          <div class="text-sm text-yellow-800 dark:text-yellow-200">
            <strong>Tip:</strong> Select groups above to apply the same changes to all wafers in those groups.
            Individual wafer modifications below will override group changes.
          </div>
        </div>
      </div>
    </div>
  `;
}

// Setup bulk modification event handlers
function setupBulkModificationHandlers(waferData, bulkAnalysis) {
  if (!bulkAnalysis.hasGroups) return;

  // Select all groups button
  const selectAllGroupsBtn = document.getElementById('select-all-groups');
  if (selectAllGroupsBtn) {
    selectAllGroupsBtn.addEventListener('click', () => {
      const groupCheckboxes = document.querySelectorAll('.bulk-group-checkbox');
      const allChecked = Array.from(groupCheckboxes).every(cb => cb.checked);

      groupCheckboxes.forEach(cb => {
        cb.checked = !allChecked;
      });

      updateBulkApplyButton();
      selectAllGroupsBtn.textContent = allChecked ? 'Select All Groups' : 'Deselect All Groups';
    });
  }

  // Apply bulk changes button
  const applyBulkBtn = document.getElementById('apply-bulk-changes');
  if (applyBulkBtn) {
    applyBulkBtn.addEventListener('click', () => {
      applyBulkModifications(waferData, bulkAnalysis);
    });
  }

  // Group checkboxes
  const groupCheckboxes = document.querySelectorAll('.bulk-group-checkbox');
  groupCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkApplyButton);
  });
}

// Update the bulk apply button state
function updateBulkApplyButton() {
  const applyBulkBtn = document.getElementById('apply-bulk-changes');
  const selectedGroups = document.querySelectorAll('.bulk-group-checkbox:checked');

  if (applyBulkBtn) {
    applyBulkBtn.disabled = selectedGroups.length === 0;
    applyBulkBtn.textContent = selectedGroups.length > 0
      ? `Apply to ${selectedGroups.length} Group(s)`
      : 'Apply to Selected Groups';
  }
}

// Toggle group details visibility
function toggleGroupDetails(groupIndex) {
  const detailsDiv = document.getElementById(`group-details-${groupIndex}`);
  const chevron = document.getElementById(`group-chevron-${groupIndex}`);

  if (detailsDiv && chevron) {
    const isHidden = detailsDiv.classList.contains('hidden');

    if (isHidden) {
      detailsDiv.classList.remove('hidden');
      chevron.classList.remove('fa-chevron-down');
      chevron.classList.add('fa-chevron-up');
    } else {
      detailsDiv.classList.add('hidden');
      chevron.classList.remove('fa-chevron-up');
      chevron.classList.add('fa-chevron-down');
    }
  }
}

// Apply bulk modifications to selected groups
function applyBulkModifications(waferData, bulkAnalysis) {
  const selectedGroupCheckboxes = document.querySelectorAll('.bulk-group-checkbox:checked');

  if (selectedGroupCheckboxes.length === 0) {
    showMessage("Please select at least one group to apply bulk changes", "warning");
    return;
  }

  // Get current form values
  const formValues = {
    xfab_id: document.getElementById('mod-xfab-id').value.trim(),
    mask_set_id: document.getElementById('mod-mask-set-id').value.trim(),
    module_name: document.getElementById('mod-module-name').value.trim(),
    lot_id: document.getElementById('mod-lot-id').value.trim(),
    cassette_id: document.getElementById('mod-cassette-id').value.trim(),
    location_id: document.getElementById('mod-location-id').value,
    arrived_at: document.getElementById('mod-arrived-at').value,
    sent_at: document.getElementById('mod-sent-at').value
  };

  // Check if any values are provided
  const hasValues = Object.values(formValues).some(value => value !== '');

  if (!hasValues) {
    showMessage("Please fill in at least one field to apply to the selected groups", "warning");
    return;
  }

  // Collect wafer IDs from selected groups
  const affectedWaferIds = [];
  selectedGroupCheckboxes.forEach(checkbox => {
    const groupIndex = parseInt(checkbox.dataset.groupIndex);
    const group = bulkAnalysis.groups[groupIndex];
    if (group) {
      group.wafers.forEach(wafer => {
        affectedWaferIds.push(wafer.wafer_id);
      });
    }
  });

  // Show confirmation dialog
  Swal.fire({
    title: 'Confirm Bulk Modification',
    html: `
      <div class="text-left">
        <p class="mb-3">You are about to apply the following changes to <strong>${affectedWaferIds.length} wafers</strong> across <strong>${selectedGroupCheckboxes.length} group(s)</strong>:</p>
        <div class="bg-gray-50 p-3 rounded border text-sm">
          ${Object.entries(formValues)
            .filter(([key, value]) => value !== '')
            .map(([key, value]) => `<div><strong>${key.replace('_', ' ').toUpperCase()}:</strong> ${value}</div>`)
            .join('')}
        </div>
        <p class="mt-3 text-sm text-gray-600">This will override individual wafer settings for the selected fields.</p>
      </div>
    `,
    icon: 'question',
    showCancelButton: true,
    confirmButtonColor: '#3b82f6',
    cancelButtonColor: '#6b7280',
    confirmButtonText: 'Apply Changes',
    cancelButtonText: 'Cancel'
  }).then((result) => {
    if (result.isConfirmed) {
      // Apply the bulk changes by pre-filling individual wafer slots
      applyBulkChangesToIndividualInputs(affectedWaferIds, formValues);
      showMessage(`Bulk changes applied to ${affectedWaferIds.length} wafers. Review and submit the form.`, 'success');
    }
  });
}

// Apply bulk changes to individual wafer inputs
function applyBulkChangesToIndividualInputs(waferIds, formValues) {
  // For slot IDs, update individual wafer slot inputs if a default slot is provided
  if (formValues.slot_id) {
    const defaultSlotValue = document.getElementById('mod-slot-id').value;
    if (defaultSlotValue) {
      waferIds.forEach(waferId => {
        const slotInput = document.querySelector(`input[data-wafer-id="${waferId}"]`);
        if (slotInput && !slotInput.value) {
          slotInput.value = defaultSlotValue;
        }
      });
    }
  }

  // Show visual feedback
  const bulkSection = document.querySelector('.bulk-modification-section');
  if (bulkSection) {
    bulkSection.style.background = 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)';
    bulkSection.style.borderColor = '#16a34a';

    setTimeout(() => {
      bulkSection.style.background = '';
      bulkSection.style.borderColor = '';
    }, 2000);
  }
}

function deleteInventory() {
  // Get selected wafer IDs
  const selectedCheckboxes = document.querySelectorAll('input[name="inventory-item"]:checked');
  const selectedWaferIds = Array.from(selectedCheckboxes).map(cb => cb.value);

  if (selectedWaferIds.length === 0) {
    showMessage("Please select at least one wafer to delete", "error");
    return;
  }

  // Use SweetAlert2 for confirmation
  Swal.fire({
    title: 'Confirm Deletion',
    html: `Are you sure you want to delete ${selectedWaferIds.length} wafer(s)?<br><br>
           <span class="text-red-500">This action cannot be undone.</span>`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#d33',
    cancelButtonColor: '#3085d6',
    confirmButtonText: 'Yes, delete',
    cancelButtonText: 'Cancel'
  }).then((result) => {
    if (result.isConfirmed) {
      // Call delete API
      deleteWafers(selectedWaferIds);
    }
  });
}

// Show modal for modifying wafers
function showModificationModal(waferIds, waferData = []) {
  if (!waferIds || waferIds.length === 0) {
    showMessage("No wafers selected", "error");
    return;
  }

  // Analyze wafer data for bulk operations
  const bulkAnalysis = analyzeBulkModificationOpportunities(waferData);

  // Create modal HTML with improved layout and fixed height
  const modalHTML = `
    <div class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-40 p-4">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-[85vh] flex flex-col">
        <!-- Fixed Header -->
        <div class="flex-shrink-0 p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex justify-between items-center">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white">
              Modify ${waferIds.length} Selected Wafer(s)
            </h3>
            <button type="button" id="close-modal-btn" class="text-gray-400 hover:text-gray-600 text-2xl">
              <i class="fas fa-times"></i>
            </button>
          </div>
          ${waferIds.length > 1 ? `
            <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Selected wafers: ${waferIds.slice(0, 5).join(', ')}${waferIds.length > 5 ? ` and ${waferIds.length - 5} more...` : ''}
            </div>
          ` : ''}
        </div>

        <!-- Scrollable Content -->
        <div class="flex-1 overflow-y-auto p-6">
          ${bulkAnalysis.hasGroups ? createBulkModificationSection(bulkAnalysis) : ''}

          <form id="modify-form" class="space-y-6">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200 dark:border-gray-700">
              <nav class="-mb-px flex space-x-8">
                <button type="button" class="tab-btn active py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600" data-tab="basic">
                  Basic Info
                </button>
                <button type="button" class="tab-btn py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="location">
                  Location & Dates
                </button>
                ${waferIds.length > 1 ? `
                  <button type="button" class="tab-btn py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="slots">
                    Individual Slots <span class="ml-1 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">${waferIds.length}</span>
                  </button>
                ` : ''}
              </nav>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
              <!-- Basic Info Tab -->
              <div id="basic-tab" class="tab-panel active">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-4">
                    <h4 class="font-medium text-lg text-gray-800 dark:text-gray-200">Wafer Details</h4>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">XFAB ID</label>
                      <input type="text" id="mod-xfab-id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="${waferData.length === 1 ? waferData[0].xfab_id : 'Multiple values...'}">
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Mask Set ID</label>
                      <input type="text" id="mod-mask-set-id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="${waferData.length === 1 ? waferData[0].mask_set_id : 'Multiple values...'}">
                    </div>
                  </div>

                  <div class="space-y-4">
                    <h4 class="font-medium text-lg text-gray-800 dark:text-gray-200">Project Details</h4>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Module Name</label>
                      <input type="text" id="mod-module-name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="${waferData.length === 1 ? waferData[0].module_name : 'Multiple values...'}">
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">LGT Lot ID</label>
                      <input type="text" id="mod-lot-id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="${waferData.length === 1 ? waferData[0].lot_id : 'Multiple values...'}">
                    </div>
                  </div>
                </div>
              </div>

              <!-- Location & Dates Tab -->
              <div id="location-tab" class="tab-panel hidden">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-4">
                    <h4 class="font-medium text-lg text-gray-800 dark:text-gray-200">Location Details</h4>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cassette ID</label>
                      <input type="text" id="mod-cassette-id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="${waferData.length === 1 ? waferData[0].cassette_id : 'Multiple values...'}">
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default Slot ID</label>
                      <input type="number" min="1" id="mod-slot-id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="${waferData.length === 1 ? waferData[0].slot_id : 'Auto-assign...'}">
                      <p class="text-xs text-gray-500 mt-1">Will apply to all wafers unless individually set</p>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Location</label>
                      <select id="mod-location-id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select Location</option>
                        <!-- Options will be loaded dynamically -->
                      </select>
                      <p class="text-xs text-gray-500 mt-1">${waferData.length === 1 ? 'Current: ' + waferData[0].location_label : 'Multiple locations...'}</p>
                    </div>
                  </div>

                  <div class="space-y-4">
                    <h4 class="font-medium text-lg text-gray-800 dark:text-gray-200">Timestamps</h4>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Arrived At</label>
                      <input type="datetime-local" id="mod-arrived-at" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <p class="text-xs text-gray-500 mt-1">${waferData.length === 1 ? 'Current: ' + waferData[0].arrived_at : 'Multiple values...'}</p>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sent At</label>
                      <input type="datetime-local" id="mod-sent-at" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <p class="text-xs text-gray-500 mt-1">${waferData.length === 1 ? 'Current: ' + waferData[0].sent_at : 'Multiple values...'}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Individual Slots Tab -->
              ${waferIds.length > 1 ? `
                <div id="slots-tab" class="tab-panel hidden">
                  <div class="space-y-4">
                    <div class="flex justify-between items-center">
                      <h4 class="font-medium text-lg text-gray-800 dark:text-gray-200">Individual Wafer Slots</h4>
                      <div class="flex gap-2">
                        <button type="button" id="auto-assign-slots" class="px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200">
                          <i class="fas fa-magic mr-1"></i>Auto-assign (1,2,3...)
                        </button>
                        <button type="button" id="clear-all-slots" class="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200">
                          <i class="fas fa-eraser mr-1"></i>Clear All
                        </button>
                      </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-64 overflow-y-auto">
                      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                        ${waferIds.map((waferId, index) => {
                          const wafer = waferData.find(w => w.wafer_id === waferId);
                          const slotValue = wafer ? wafer.slot_id : '';

                          return `
                            <div class="flex items-center gap-2 bg-white dark:bg-gray-800 p-2 rounded border">
                              <span class="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-0 flex-1 truncate" title="${waferId}">${waferId}</span>
                              <input type="number" min="1" id="slot-${index}" data-wafer-id="${waferId}"
                                     class="wafer-slot px-2 py-1 border border-gray-300 rounded w-16 text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                     value="${slotValue}" placeholder="Slot">
                            </div>
                          `;
                        }).join('')}
                      </div>
                    </div>

                    <div class="text-sm text-gray-600 dark:text-gray-400">
                      <p><i class="fas fa-info-circle mr-1"></i>Leave empty to use the default slot ID from Location & Dates tab</p>
                    </div>
                  </div>
                </div>
              ` : ''}
            </div>
          </form>
        </div>

        <!-- Fixed Footer -->
        <div class="flex-shrink-0 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
          <div class="flex justify-end gap-3">
            <button type="button" id="cancel-btn" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-gray-500">
              Cancel
            </button>
            <button type="submit" form="modify-form" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500">
              <i class="fas fa-save mr-2"></i>Apply Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Add modal to DOM
  const modalContainer = document.createElement('div');
  modalContainer.innerHTML = modalHTML;
  document.body.appendChild(modalContainer);

  // Get form elements
  const modifyForm = document.getElementById('modify-form');
  const cancelBtn = document.getElementById('cancel-btn');
  const closeBtn = document.getElementById('close-modal-btn');

  // Set up tab functionality
  setupTabNavigation();

  // Set up slot management buttons
  setupSlotManagement(waferIds);

  // Populate form fields if we have a single wafer selected
  if (waferData.length === 1) {
    const wafer = waferData[0];

    // Pre-populate the form fields with current values
    document.getElementById('mod-xfab-id').value = wafer.xfab_id;
    document.getElementById('mod-mask-set-id').value = wafer.mask_set_id;
    document.getElementById('mod-module-name').value = wafer.module_name;
    document.getElementById('mod-lot-id').value = wafer.lot_id;
    document.getElementById('mod-cassette-id').value = wafer.cassette_id;
    document.getElementById('mod-slot-id').value = wafer.slot_id;

    // For the date fields, use a consistent format for datetime-local input
    try {
      if (wafer.arrived_at) {
        // Simplify date handling by extracting date portion if it's in ISO format
        if (wafer.arrived_at.includes('T')) {
          const datePart = wafer.arrived_at.split('T')[0];
          const timePart = wafer.arrived_at.includes(':') ? 
                          wafer.arrived_at.split('T')[1].substr(0, 5) : // Take HH:MM
                          "00:00"; // Default time if not available
          document.getElementById('mod-arrived-at').value = `${datePart}T${timePart}`;
          console.log(`Set arrived_at to ${datePart}T${timePart}`);
        } else {
          // If not in ISO format, try as regular date
          const arrivedDate = new Date(wafer.arrived_at);
          if (!isNaN(arrivedDate.getTime())) {
            const formatted = arrivedDate.toISOString().slice(0, 16);
            document.getElementById('mod-arrived-at').value = formatted;
            console.log(`Set arrived_at to ${formatted} (from Date object)`);
          }
        }
      }

      if (wafer.sent_at) {
        // Same approach for sent_at
        if (wafer.sent_at.includes('T')) {
          const datePart = wafer.sent_at.split('T')[0];
          const timePart = wafer.sent_at.includes(':') ? 
                          wafer.sent_at.split('T')[1].substr(0, 5) : 
                          "00:00";
          document.getElementById('mod-sent-at').value = `${datePart}T${timePart}`;
          console.log(`Set sent_at to ${datePart}T${timePart}`);
        } else {
          const sentDate = new Date(wafer.sent_at);
          if (!isNaN(sentDate.getTime())) {
            const formatted = sentDate.toISOString().slice(0, 16);
            document.getElementById('mod-sent-at').value = formatted;
            console.log(`Set sent_at to ${formatted} (from Date object)`);
          }
        }
      }
    } catch (e) {
      console.error("Error formatting dates for form:", e);
    }
  }

  // Load locations
  loadLocationsForDropdown('mod-location-id').then(() => {
    // Set location after dropdown is populated (if single wafer)
    if (waferData.length === 1 && waferData[0].location_id) {
      // Find the option with the matching location_id and select it
      const locationSelect = document.getElementById('mod-location-id');
      const matchingOption = Array.from(locationSelect.options).find(
        option => option.value === waferData[0].location_id
      );

      if (matchingOption) {
        matchingOption.selected = true;
      }
    }
  });

  // Set up bulk modification handlers
  setupBulkModificationHandlers(waferData, bulkAnalysis);

  // Set up individual slot inputs
  const slotInputs = document.querySelectorAll('.wafer-slot');
  const defaultSlotInput = document.getElementById('mod-slot-id');

  // When default slot changes, update empty individual slots
  defaultSlotInput.addEventListener('change', () => {
    const defaultValue = defaultSlotInput.value;
    if (defaultValue) {
      slotInputs.forEach(input => {
        if (!input.value) {
          input.value = defaultValue;
        }
      });
    }
  });

  // Close and cancel button handlers
  const closeModal = () => modalContainer.remove();
  cancelBtn.addEventListener('click', closeModal);
  closeBtn.addEventListener('click', closeModal);

  // Form submission
  modifyForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    // Initialize progress indicator
    const progress = window.progressIndicator || new ProgressIndicator();
    const steps = [
      'Collecting form data',
      'Validating changes',
      'Updating database',
      'Refreshing display'
    ];
    progress.init(`Modifying ${waferIds.length} Wafer(s)`, steps);

    try {
      // Step 1: Collect data
      progress.updateProgress(0);

      // Collect individual wafer slot updates
      const waferUpdates = {};
      slotInputs.forEach(input => {
        const waferId = input.dataset.waferId;
        const slotValue = input.value;
        if (slotValue) {
          waferUpdates[waferId] = {
            slot_id: parseInt(slotValue)
          };
        }
      });

      // Collect form data
      const formData = {
        wafer_ids: waferIds,
        wafer_updates: waferUpdates,
        // Only include non-empty fields
        xfab_id: document.getElementById('mod-xfab-id').value.trim(),
        mask_set_id: document.getElementById('mod-mask-set-id').value.trim(),
        module_name: document.getElementById('mod-module-name').value.trim(),
        lot_id: document.getElementById('mod-lot-id').value.trim(),
        cassette_id: document.getElementById('mod-cassette-id').value.trim(),
        location_id: document.getElementById('mod-location-id').value,
        arrived_at: document.getElementById('mod-arrived-at').value,
        sent_at: document.getElementById('mod-sent-at').value,
        updated_by: 'operations_fr_inventory'
      };

      // Step 2: Validate
      progress.nextStep('Validating changes...');

      // Filter out empty fields
      Object.keys(formData).forEach(key => {
        if (formData[key] === "" && key !== 'arrived_at' && key !== 'sent_at') {
          delete formData[key];
        }
      });

      // Step 3: Submit the update
      progress.nextStep('Updating database...');

      const response = await fetch(ENDPOINTS.MODIFY, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        // Step 4: Complete and refresh
        progress.nextStep('Refreshing display...');

        showMessage(result.message || 'Changes applied successfully', 'success');
        modalContainer.remove();

        // Set flag to force refresh and reload page
        sessionStorage.setItem('justModifiedInventory', 'true');
        sessionStorage.setItem('lastModifiedTimestamp', Date.now().toString());

        progress.complete('Changes applied successfully!');

        // Force page reload with cache busting
        setTimeout(() => {
          const url = new URL(window.location.href);
          url.searchParams.set('fresh', Date.now().toString());
          url.searchParams.set('update', Math.random().toString(36).substring(2));
          window.location.href = url.toString();
        }, 1000);
      } else {
        // Check if this is a special error that requires a prominent alert
        if (result.show_alert === true && typeof Swal !== 'undefined') {
          // Use a prominent SweetAlert for critical errors like foreign key violations
          // Format the message for better readability
          const formattedMessage = (result.message || 'Failed to apply changes')
            .replace(/ERREUR:/g, '<strong>ERROR:</strong>')
            .replace(/fk_wafer_inventory_wafer_id_wafers/g, '<em>wafer_inventory → wafers</em>')
            .replace(/wafer_inventory/g, '<strong>wafer_inventory</strong>')
            .replace(/wafers/g, '<strong>wafers</strong>');

          Swal.fire({
            icon: 'error',
            title: 'Database Constraint Violation',
            html: `
              <div style="text-align: left; line-height: 1.6;">
                <p style="margin-bottom: 15px; font-size: 16px;">
                  <strong>Unable to modify inventory</strong>
                </p>
                <div style="background-color: #fee; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #dc2626;">
                  <p style="margin: 0; font-size: 14px; color: #7f1d1d;">
                    ${formattedMessage}
                  </p>
                </div>
                <p style="margin-bottom: 10px; font-size: 14px;">
                  <strong>What this means:</strong>
                </p>
                <ul style="margin: 0; padding-left: 20px; font-size: 14px;">
                  <li>The wafer(s) you're trying to modify don't exist in the main wafers table</li>
                  <li>Foreign key constraints prevent modifying non-existent wafers in inventory</li>
                </ul>
                <p style="margin-top: 15px; margin-bottom: 0; font-size: 14px;">
                  <strong>Solution:</strong> Ensure the wafer(s) exist in the main wafers table first.
                </p>
              </div>
            `,
            confirmButtonText: 'I Understand',
            confirmButtonColor: '#dc2626',
            allowOutsideClick: false,
            allowEscapeKey: false,
            width: '600px',
            timer: 15000, // Auto-close after 15 seconds
            timerProgressBar: true,
            showCloseButton: true,
            customClass: {
              popup: 'text-left'
            }
          });
        } else {
          // Use regular showMessage for other errors
          showMessage(result.message || 'Failed to apply changes', 'error');
        }
      }
    } catch (error) {
      console.error('Error updating inventory:', error);
      progress.error(`Update failed: ${error.message}`);
      showMessage(`Error: ${error.message}`, 'error');
    }
  });
}

// Helper function to set up tab navigation
function setupTabNavigation() {
  const tabButtons = document.querySelectorAll('.tab-btn');
  const tabPanels = document.querySelectorAll('.tab-panel');

  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const targetTab = button.dataset.tab;

      // Remove active class from all buttons and panels
      tabButtons.forEach(btn => {
        btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
        btn.classList.add('border-transparent', 'text-gray-500');
      });
      tabPanels.forEach(panel => {
        panel.classList.add('hidden');
        panel.classList.remove('active');
      });

      // Add active class to clicked button and corresponding panel
      button.classList.add('active', 'border-blue-500', 'text-blue-600');
      button.classList.remove('border-transparent', 'text-gray-500');

      const targetPanel = document.getElementById(`${targetTab}-tab`);
      if (targetPanel) {
        targetPanel.classList.remove('hidden');
        targetPanel.classList.add('active');
      }
    });
  });
}

// Helper function to set up slot management
function setupSlotManagement(waferIds) {
  const autoAssignBtn = document.getElementById('auto-assign-slots');
  const clearAllBtn = document.getElementById('clear-all-slots');

  if (autoAssignBtn) {
    autoAssignBtn.addEventListener('click', () => {
      const slotInputs = document.querySelectorAll('.wafer-slot');
      slotInputs.forEach((input, index) => {
        input.value = index + 1;
      });
      showMessage(`Auto-assigned slots 1-${slotInputs.length}`, 'success');
    });
  }

  if (clearAllBtn) {
    clearAllBtn.addEventListener('click', () => {
      const slotInputs = document.querySelectorAll('.wafer-slot');
      slotInputs.forEach(input => {
        input.value = '';
      });
      showMessage('Cleared all individual slot assignments', 'info');
    });
  }
}

// Load locations for dropdown
async function loadLocationsForDropdown(elementId) {
  const dropdown = document.getElementById(elementId);

  try {
    dropdown.innerHTML = '<option value="">Loading locations...</option>';

    const response = await fetch('/api/locations/list', {
      method: 'GET',
      headers: {
        'X-CSRFToken': getCSRFToken(),
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to load locations: ${response.status}`);
    }

    const data = await response.json();

    dropdown.innerHTML = '<option value="">Select location</option>';

    if (data.locations && Array.isArray(data.locations)) {
      data.locations.forEach(location => {
        const option = document.createElement('option');
        option.value = location.location_id;
        option.textContent = location.label || location.location_id;
        dropdown.appendChild(option);
      });
    }

    return data.locations || [];
  } catch (error) {
    console.error('Error loading locations:', error);
    dropdown.innerHTML = '<option value="">Failed to load locations</option>';
    return [];
  }
}

// Function to delete wafers
async function deleteWafers(waferIds) {
  try {
    // Show loading indicator
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50';
    loadingOverlay.innerHTML = `
      <div class="bg-white p-4 rounded-lg">
        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p class="mt-2 text-gray-700">Deleting wafers...</p>
      </div>
    `;
    document.body.appendChild(loadingOverlay);

    // Call delete API
    const response = await fetch(ENDPOINTS.DELETE, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCSRFToken(),
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        wafer_ids: waferIds
      })
    });

    loadingOverlay.remove();

    if (!response.ok) {
      throw new Error(`Error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    if (result.success) {
      showMessage(`Successfully deleted ${result.deleted_wafers.length} wafer(s)`, 'success');
      searchInventory(); // Refresh the table
    } else {
      showMessage(result.message || 'Failed to delete wafers', 'error');
    }
  } catch (error) {
    console.error('Error deleting wafers:', error);
    showMessage(`Error: ${error.message}`, 'error');
  }
}

// Core Functionality
async function searchInventory(forceRefresh = false) {
  try {
    const searchParams = getSearchParams();

    // Add cache-busting timestamp with more randomness
    searchParams.timestamp = new Date().getTime() + Math.floor(Math.random() * 1000000);

    // If forcing a refresh, add even more cache busting
    if (forceRefresh) {
      searchParams.forceRefresh = true;
      searchParams.cacheBuster = crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36);
    }

    // Log for debugging
    console.log("Search with cache-busting:", searchParams);

    const loadingOverlay = document.createElement("div");
    loadingOverlay.className =
      "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
    loadingOverlay.innerHTML = `
            <div class="bg-white p-4 rounded-lg">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
                <p class="mt-2 text-gray-700">Searching...</p>
            </div>
        `;
    document.body.appendChild(loadingOverlay);

    // Create headers with cache-busting but without CSRF token for API routes
    const headers = {
      "Content-Type": "application/json",
      "X-Requested-With": "XMLHttpRequest",
      "Cache-Control": "no-cache, no-store, must-revalidate, max-age=0",
      "Pragma": "no-cache",
      "Expires": "0",
      "X-Cache-Bust": Date.now().toString()
    };
    
    // Add CSRF token only for non-API routes (to prevent issues)
    if (!ENDPOINTS.SEARCH.startsWith('/api/')) {
      headers["X-CSRFToken"] = getCSRFToken();
    }

    // For forced refreshes, add even more aggressive cache-busting
    if (forceRefresh) {
      headers["X-Force-Fresh"] = "true";
      headers["X-Random"] = Math.random().toString();
    }

    const response = await fetch(ENDPOINTS.SEARCH, {
      method: "POST",
      headers: headers,
      credentials: "same-origin",
      body: JSON.stringify({
        ...searchParams,
        page: currentPage,
        page_size: pageSize,
      }),
    });

    loadingOverlay.remove();

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Check if response is actually JSON
    const contentType = response.headers.get("content-type");
    if (!contentType || !contentType.includes("application/json")) {
      throw new Error(
        "Server returned non-JSON response. Please check your authentication status."
      );
    }

    const data = await response.json();
    console.log("Search results:", data);

    if (!data.success) {
      throw new Error(data.message || "Search failed");
    }

    // Update the table with search results
    if (data.data && Array.isArray(data.data)) {
      updateTable(data.data, data.pagination);
    } else {
      updateTable([], { total: 0 });
    }

    // If this was a forced refresh, show a success message
    if (forceRefresh) {
      showMessage("Data refreshed successfully", "success");
    }
  } catch (error) {
    console.error("Search error:", error);
    showMessage(error.message || "Search failed", "error");
    // Reset the table on error
    updateTable([], { total: 0 });
  }
}

// Function to initialize empty table with helpful message
function initializeEmptyTable() {
  const tableBody = document.getElementById('inventory-table-body');
  const resultsInfo = document.getElementById('results-info');
  const paginationInfo = document.getElementById('pagination-info');

  if (tableBody) {
    tableBody.innerHTML = `
      <tr>
        <td colspan="12" class="px-6 py-12 text-center">
          <div class="flex flex-col items-center justify-center space-y-4">
            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <i class="fas fa-search text-2xl text-gray-400"></i>
            </div>
            <div class="text-center">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Ready to Search Inventory
              </h3>
              <p class="text-gray-500 dark:text-gray-400 mb-4 max-w-md">
                Use the search bar above or click a quick filter to find inventory items.
                You can search by wafer ID, lot ID, location, or any other field.
              </p>
              <div class="flex flex-wrap justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <span class="inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 rounded-md">
                  <i class="fas fa-clock mr-1"></i>Recent Arrivals
                </span>
                <span class="inline-flex items-center px-2 py-1 bg-green-100 dark:bg-green-900 rounded-md">
                  <i class="fas fa-shipping-fast mr-1"></i>Recently Shipped
                </span>
                <span class="inline-flex items-center px-2 py-1 bg-purple-100 dark:bg-purple-900 rounded-md">
                  <i class="fas fa-warehouse mr-1"></i>Available Stock
                </span>
              </div>
              <p class="text-xs text-gray-400 mt-3">
                💡 Tip: Press <kbd class="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Ctrl+K</kbd> to quickly focus the search bar
              </p>
            </div>
          </div>
        </td>
      </tr>
    `;
  }

  // Update results info
  if (resultsInfo) {
    resultsInfo.textContent = 'Ready to search - enter criteria above';
  }

  // Hide pagination info
  if (paginationInfo) {
    paginationInfo.style.display = 'none';
  }

  // Reset pagination controls
  const prevPageBtn = document.getElementById('prev-page');
  const nextPageBtn = document.getElementById('next-page');
  const pageInfo = document.getElementById('page-info');

  if (prevPageBtn) prevPageBtn.disabled = true;
  if (nextPageBtn) nextPageBtn.disabled = true;
  if (pageInfo) pageInfo.textContent = '';

  console.log('📋 Empty table initialized with search guidance');
}

// Initialize pagination controls on load
initializePagination();

// Initialize empty table with helpful message
initializeEmptyTable();

// When page fully loaded, initialize more complex UI
window.addEventListener("load", function () {
  setupStickyHeader();
  setupLocationOptions();
});

// Table header sticky behavior
function setupStickyHeader() {
  const header = document.querySelector(".sticky-header");
  if (header) {
    window.addEventListener("scroll", function () {
      const tableContainer = header.closest(".table-container");
      if (tableContainer) {
        if (tableContainer.scrollTop > 0) {
          header.classList.add("shadow-md", "bg-white", "dark:bg-gray-800");
        } else {
          header.classList.remove("shadow-md");
        }
      }
    });
  }
}

// Load locations for dropdown
async function setupLocationOptions() {
  try {
    // Show loading in location dropdown
    const locationSelect = document.getElementById("location-id");
    if (!locationSelect) return;

    locationSelect.disabled = true;
    locationSelect.innerHTML = '<option value="">Loading locations...</option>';

    // Fetch locations from server
    const response = await fetch("/api/locations/list", {
      method: "GET",
      credentials: "same-origin",
      headers: {
        "X-CSRFToken": getCSRFToken(),
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to load locations: ${response.status}`);
    }

    const data = await response.json();
    if (!data.success) {
      throw new Error(data.message || "Failed to load locations");
    }

    // Reset dropdown
    locationSelect.innerHTML = '<option value="">All Locations</option>';

    // Add locations to dropdown
    if (data.locations && Array.isArray(data.locations)) {
      data.locations.forEach((location) => {
        const option = document.createElement("option");
        option.value = location.location_id;
        option.textContent = location.label || location.location_id;
        locationSelect.appendChild(option);
      });
    }

    // Re-enable dropdown
    locationSelect.disabled = false;
  } catch (error) {
    console.error("Error loading locations:", error);
    // Reset dropdown with error message
    const locationSelect = document.getElementById("location-id");
    if (locationSelect) {
      locationSelect.innerHTML =
        '<option value="">Failed to load locations</option>';
      locationSelect.disabled = false;
    }
  }
}

// Export to CSV functionality
async function exportToCSV() {
  try {
    // Show loading indicator
    const loadingOverlay = document.createElement("div");
    loadingOverlay.className =
      "loading-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
    loadingOverlay.innerHTML = `
            <div class="bg-white p-4 rounded-lg">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
                <p class="mt-2 text-gray-700">Exporting data...</p>
            </div>
        `;
    document.body.appendChild(loadingOverlay);

    // Get current search params for the export
    const searchParams = getSearchParams();

    // Make export API call
    const response = await fetch(ENDPOINTS.EXPORT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCSRFToken(),
      },
      body: JSON.stringify(searchParams),
    });

    if (!response.ok) {
      throw new Error(`Failed to export data: ${response.status}`);
    }

    const data = await response.json();
    if (!data.success) {
      throw new Error(data.message || "Export failed");
    }

    // Convert data to CSV
    const csvContent = convertToCSV(data.data);

    // Download the CSV file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    const timestamp = new Date()
      .toISOString()
      .replace(/:/g, "-")
      .replace(/\..+/, "");
    link.setAttribute("href", url);
    link.setAttribute("download", `inventory_export_${timestamp}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    loadingOverlay.remove();
    showMessage("Export completed successfully", "success");
  } catch (error) {
    console.error("Export error:", error);
    document.querySelector(".loading-overlay")?.remove();
    showMessage(error.message || "Failed to export data", "error");
  }
}

function getInputValue(id) {
  return document.getElementById(id)?.value || "";
}

function convertToCSV(data) {
  if (data.length === 0) {
    return "";
  }

  // Define columns for the CSV
  const columns = [
    "wafer_id",
    "lot_id",
    "xfab_id",
    "mask_set_id",
    "module_name",
    "cassette_id",
    "slot_id",
    "location_id",
    "arrived_at",
    "sent_at",
  ];

  // Create header row
  let csv = columns.map((col) => `"${col}"`).join(",") + "\n";

  // Add data rows
  data.forEach((item) => {
    const row = columns.map((col) => {
      // Handle special cases for dates
      if (col === "arrived_at" || col === "sent_at") {
        return item[col] ? `"${formatDate(item[col])}"` : '""';
      }
      return `"${item[col] || ""}"`;
    });
    csv += row.join(",") + "\n";
  });

  return csv;
}

// Utility function to force refresh inventory data
function forceRefreshInventoryData() {
  console.log("Force refreshing inventory data...");

  // Show loading indicator
  const loadingOverlay = document.createElement('div');
  loadingOverlay.className = 'loading-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
  loadingOverlay.innerHTML = `
    <div class="bg-white p-4 rounded-lg shadow-md">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
      <p class="mt-2 text-gray-700">Refreshing inventory data...</p>
    </div>
  `;
  document.body.appendChild(loadingOverlay);

  // Clear any cached data that might be affecting the display
  window.cachedInventoryData = null;

  // We'll skip browser cache clearing as it's not critical
  // and causing errors in some environments
  /*
  // This code is disabled to avoid errors in environments without Cache API
  try {
    if (typeof caches !== 'undefined' && 'caches' in window) {
      console.log('Cache API available, attempting to clear caches');
      // Rest of cache clearing code...
    } else {
      console.log('Cache API not available in this environment');
    }
  } catch (e) {
    console.warn('Error with cache operations:', e);
  }
  */

  // Completely clear DOM cache to force a fresh render
  document.getElementById("inventory-table-body").innerHTML = "";

  // Make the API call with aggressive cache-busting headers
  fetch(ENDPOINTS.SEARCH, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": getCSRFToken(),
      "X-Requested-With": "XMLHttpRequest",
      "Cache-Control": "no-cache, no-store, must-revalidate, max-age=0",
      "Pragma": "no-cache",
      "Expires": "0",
      "X-Cache-Bust": Date.now().toString(),
      "X-Force-Refresh": "true",
      "X-Timestamp": new Date().toISOString()
    },
    credentials: "same-origin",
    body: JSON.stringify({
      ...getSearchParams(),
      timestamp: Date.now(),
      force_refresh: true,
      cache_bust: Math.random().toString(36)
    }),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then((data) => {
      // Remove loading overlay
      loadingOverlay.remove();

      if (!data.success) {
        throw new Error(data.message || "Failed to refresh data");
      }

      console.log("Refresh successful, updating table with fresh data");
      console.log("Fresh data:", data);

      // Update the table with fresh data instead of full page refresh
      updateTable(data.data, data.pagination);

      // Show success message
      showMessage("Inventory data refreshed successfully", "success");
    })
    .catch((error) => {
      console.error("Error refreshing data:", error);
      document.querySelector(".loading-overlay")?.remove();
      showMessage(error.message || "Failed to refresh data", "error");
    });
}